/**
 * Connectivity Management System untuk PPWA
 * Mengelola status koneksi, sync otomatis, dan notifikasi status
 */

import { storage } from './storage';

export interface ConnectivityStatus {
  isOnline: boolean;
  lastOnline: number;
  lastSync: number;
  syncInProgress: boolean;
  connectionType?: string;
  effectiveType?: string;
}

export interface SyncResult {
  success: boolean;
  itemsSynced: number;
  errors: string[];
  timestamp: number;
}

class ConnectivityManager {
  private status: ConnectivityStatus = {
    isOnline: navigator.onLine,
    lastOnline: Date.now(),
    lastSync: 0,
    syncInProgress: false
  };

  private syncInterval: number = 10 * 60 * 1000; // 10 minutes in milliseconds
  private syncTimer: any | null = null;
  private listeners: ((status: ConnectivityStatus) => void)[] = [];
  private toastContainer: HTMLElement | null = null;

  constructor() {
    this.init();
  }

  /**
   * Initialize connectivity manager
   */
  private async init(): Promise<void> {
    // Setup event listeners
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Get connection info if available
    this.updateConnectionInfo();

    // Load last sync time from storage
    const lastSync = await storage.getAppState('lastSync');
    if (lastSync) {
      this.status.lastSync = lastSync;
    }

    // Start periodic sync if online
    if (this.status.isOnline) {
      this.startPeriodicSync();
    }

    // Create toast container
    this.createToastContainer();

    console.log('[Connectivity] Manager initialized');
  }

  /**
   * Handle online event
   */
  private async handleOnline(): Promise<void> {
    console.log('[Connectivity] Device is online');
    
    this.status.isOnline = true;
    this.status.lastOnline = Date.now();
    this.updateConnectionInfo();

    // Show online notification
    this.showToast('🟢 Kembali online - Sinkronisasi data...', 'success');

    // Start background sync
    await this.performBackgroundSync();

    // Start periodic sync
    this.startPeriodicSync();

    // Notify listeners
    this.notifyListeners();
  }

  /**
   * Handle offline event
   */
  private handleOffline(): void {
    console.log('[Connectivity] Device is offline');
    
    this.status.isOnline = false;
    this.updateConnectionInfo();

    // Show offline notification
    this.showToast('🔴 Offline - Menggunakan data tersimpan', 'warning');

    // Stop periodic sync
    this.stopPeriodicSync();

    // Notify listeners
    this.notifyListeners();
  }

  /**
   * Update connection information
   */
  private updateConnectionInfo(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.status.connectionType = connection?.type || 'unknown';
      this.status.effectiveType = connection?.effectiveType || 'unknown';
    }
  }

  /**
   * Start periodic sync timer
   */
  private startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      if (this.status.isOnline && !this.status.syncInProgress) {
        console.log('[Connectivity] Performing periodic sync');
        await this.performBackgroundSync();
      }
    }, this.syncInterval);
  }

  /**
   * Stop periodic sync timer
   */
  private stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * Perform background data synchronization
   */
  async performBackgroundSync(): Promise<SyncResult> {
    if (this.status.syncInProgress) {
      console.log('[Connectivity] Sync already in progress');
      return {
        success: false,
        itemsSynced: 0,
        errors: ['Sync already in progress'],
        timestamp: Date.now()
      };
    }

    this.status.syncInProgress = true;
    this.notifyListeners();

    const result: SyncResult = {
      success: true,
      itemsSynced: 0,
      errors: [],
      timestamp: Date.now()
    };

    try {
      console.log('[Connectivity] Starting background sync...');

      // Get pending sync items
      const pendingItems = await storage.getPendingSyncItems();
      console.log(`[Connectivity] Found ${pendingItems.length} items to sync`);

      // Sync each item
      for (const item of pendingItems) {
        try {
          await this.syncItem(item);
          await storage.markSyncCompleted(item.id);
          result.itemsSynced++;
        } catch (error) {
          console.error('[Connectivity] Failed to sync item:', item.id, error);
          result.errors.push(`Failed to sync item ${item.id}: ${error}`);
        }
      }

      // Update cached data
      await this.updateCachedData();

      // Save last sync time
      this.status.lastSync = Date.now();
      await storage.saveAppState('lastSync', this.status.lastSync);

      console.log(`[Connectivity] Sync completed: ${result.itemsSynced} items synced`);

      if (result.itemsSynced > 0) {
        this.showToast(`✅ Data tersinkronisasi (${result.itemsSynced} item)`, 'success');
      }

    } catch (error) {
      console.error('[Connectivity] Background sync failed:', error);
      result.success = false;
      result.errors.push(`Sync failed: ${error}`);
      
      this.showToast('❌ Gagal sinkronisasi data', 'error');
    } finally {
      this.status.syncInProgress = false;
      this.notifyListeners();
    }

    return result;
  }

  /**
   * Sync individual item
   */
  private async syncItem(item: any): Promise<void> {
    // TODO: Implement actual API calls based on item type
    console.log('[Connectivity] Syncing item:', item);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // For now, just mark as synced
    // In real implementation, this would make actual API calls
  }

  /**
   * Update cached data from server
   */
  private async updateCachedData(): Promise<void> {
    try {
      // TODO: Implement actual data fetching from API
      console.log('[Connectivity] Updating cached data...');
      
      // For now, just update timestamp
      await storage.saveAppState('lastDataUpdate', Date.now());
      
    } catch (error) {
      console.error('[Connectivity] Failed to update cached data:', error);
    }
  }

  /**
   * Create toast notification container
   */
  private createToastContainer(): void {
    this.toastContainer = document.createElement('div');
    this.toastContainer.id = 'connectivity-toast-container';
    this.toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
    document.body.appendChild(this.toastContainer);
  }

  /**
   * Show toast notification
   */
  private showToast(message: string, type: 'success' | 'warning' | 'error' = 'success'): void {
    if (!this.toastContainer) return;

    const toast = document.createElement('div');
    const bgColor = {
      success: 'bg-success text-success-content',
      warning: 'bg-warning text-warning-content',
      error: 'bg-error text-error-content'
    }[type];

    toast.className = `alert ${bgColor} shadow-lg max-w-sm animate-slide-in-right`;
    toast.innerHTML = `
      <div class="flex items-center">
        <span class="text-sm font-medium">${message}</span>
        <button class="btn btn-ghost btn-xs ml-2" onclick="this.parentElement.parentElement.remove()">✕</button>
      </div>
    `;

    this.toastContainer.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.classList.add('animate-slide-out-right');
        setTimeout(() => toast.remove(), 300);
      }
    }, 5000);
  }

  /**
   * Get current connectivity status
   */
  getStatus(): ConnectivityStatus {
    return { ...this.status };
  }

  /**
   * Add status change listener
   */
  addListener(callback: (status: ConnectivityStatus) => void): void {
    this.listeners.push(callback);
  }

  /**
   * Remove status change listener
   */
  removeListener(callback: (status: ConnectivityStatus) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners of status change
   */
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.status);
      } catch (error) {
        console.error('[Connectivity] Error in listener callback:', error);
      }
    });
  }

  /**
   * Force sync now
   */
  async forcSync(): Promise<SyncResult> {
    if (!this.status.isOnline) {
      return {
        success: false,
        itemsSynced: 0,
        errors: ['Device is offline'],
        timestamp: Date.now()
      };
    }

    return await this.performBackgroundSync();
  }

  /**
   * Set sync interval
   */
  setSyncInterval(minutes: number): void {
    this.syncInterval = minutes * 60 * 1000;
    
    if (this.status.isOnline) {
      this.startPeriodicSync();
    }
    
    console.log(`[Connectivity] Sync interval set to ${minutes} minutes`);
  }

  /**
   * Get time since last sync in human readable format
   */
  getTimeSinceLastSync(): string {
    if (this.status.lastSync === 0) {
      return 'Belum pernah sinkronisasi';
    }

    const diff = Date.now() - this.status.lastSync;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} hari yang lalu`;
    } else if (hours > 0) {
      return `${hours} jam yang lalu`;
    } else if (minutes > 0) {
      return `${minutes} menit yang lalu`;
    } else {
      return 'Baru saja';
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    window.removeEventListener('online', this.handleOnline.bind(this));
    window.removeEventListener('offline', this.handleOffline.bind(this));
    this.stopPeriodicSync();
    
    if (this.toastContainer) {
      this.toastContainer.remove();
    }
    
    this.listeners = [];
    console.log('[Connectivity] Manager destroyed');
  }
}

// Export singleton instance
export const connectivityManager = new ConnectivityManager();
