import React, { useRef, useState, useEffect, useCallback } from 'react';
import './Homepage.css';

/**
 * Interface untuk data produk carousel
 * Mendefinisikan struktur data untuk setiap item produk
 */
interface ProductItem {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: {
    usedTime: string;
    chargingPort: string;
    compatible: string;
    bluetooth: string;
    controlled: string;
  };
}

/**
 * Komponen Homepage dengan carousel produk
 * Menampilkan slider produk dengan animasi dan detail produk
 */
const Homepage: React.FC = () => {
  // State untuk mengontrol carousel
  const [isShowingDetail, setIsShowingDetail] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);

  // State untuk date/time
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // Refs untuk manipulasi DOM - separate refs for each layout
  const carouselRefDesktop = useRef<HTMLDivElement>(null);
  const listRefDesktop = useRef<HTMLDivElement>(null);
  const carouselRefTablet = useRef<HTMLDivElement>(null);
  const listRefTablet = useRef<HTMLDivElement>(null);
  const carouselRefMobile = useRef<HTMLDivElement>(null);
  const listRefMobile = useRef<HTMLDivElement>(null);

  // Auto-rotation timer ref
  const autoRotationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Update date/time setiap detik
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Helper functions untuk format date/time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Data produk untuk carousel
  const products: ProductItem[] = [
    {
      id: 1,
      image: '/images/product/img1.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 2,
      image: '/images/product/img2.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 3,
      image: '/images/product/img3.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 4,
      image: '/images/product/img4.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    }
  ];

  /**
   * Fungsi untuk menggeser carousel ke slide berikutnya atau sebelumnya
   * @param direction - 'next' untuk slide berikutnya, 'prev' untuk slide sebelumnya
   * @param isAutoRotation - apakah ini dipanggil dari auto-rotation
   */
  const showSlider = useCallback((direction: 'next' | 'prev', isAutoRotation: boolean = false) => {
    if (isAnimating || isShowingDetail) return;

    // Reset auto-rotation timer jika ini bukan auto-rotation
    if (!isAutoRotation && autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
      startAutoRotation();
    }

    setIsAnimating(true);

    // Get all carousel refs and apply animation to all visible layouts
    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];
    const listRefs = [listRefDesktop, listRefTablet, listRefMobile];

    carouselRefs.forEach((carouselRef, index) => {
      const listRef = listRefs[index];
      if (!listRef.current || !carouselRef.current) return;

      const items = listRef.current.querySelectorAll('.carousel-item');
      const carousel = carouselRef.current;

      // Hapus kelas animasi sebelumnya
      carousel.classList.remove('next', 'prev');

      if (direction === 'next') {
        // Pindahkan item pertama ke akhir
        if (items[0]) {
          listRef.current.appendChild(items[0]);
        }
        carousel.classList.add('next');
        setCurrentProductIndex(prev => (prev + 1) % products.length);
      } else {
        // Pindahkan item terakhir ke awal
        if (items[items.length - 1]) {
          listRef.current.prepend(items[items.length - 1]);
        }
        carousel.classList.add('prev');
        setCurrentProductIndex(prev => (prev - 1 + products.length) % products.length);
      }
    });

    // Reset animasi setelah 2 detik
    setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
  }, [isAnimating, isShowingDetail, products.length]);

  /**
   * Fungsi untuk memulai auto-rotation carousel
   */
  const startAutoRotation = useCallback(() => {
    if (autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
    }

    autoRotationTimerRef.current = setTimeout(() => {
      if (!isShowingDetail && !isAnimating) {
        console.log('Auto-rotating to next product...');
        showSlider('next', true);
      }
      startAutoRotation(); // Restart timer
    }, 10000); // 10 seconds
  }, [isShowingDetail, isAnimating, showSlider]);

  /**
   * Fungsi untuk menghentikan auto-rotation
   */
  const stopAutoRotation = useCallback(() => {
    if (autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
      autoRotationTimerRef.current = null;
    }
  }, []);

  // Start auto-rotation when component mounts
  useEffect(() => {
    startAutoRotation();

    return () => {
      stopAutoRotation();
    };
  }, [startAutoRotation, stopAutoRotation]);

  /**
   * Fungsi untuk menampilkan detail produk
   */
  const showDetail = useCallback(() => {
    // Stop auto-rotation when showing detail
    stopAutoRotation();

    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];

    carouselRefs.forEach((carouselRef) => {
      if (!carouselRef.current) return;

      carouselRef.current.classList.remove('next', 'prev');
      carouselRef.current.classList.add('showDetail');
    });

    setIsShowingDetail(true);
  }, [stopAutoRotation]);

  /**
   * Fungsi untuk kembali ke tampilan carousel
   */
  const hideDetail = useCallback(() => {
    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];

    carouselRefs.forEach((carouselRef) => {
      if (!carouselRef.current) return;

      carouselRef.current.classList.remove('showDetail');
    });

    setIsShowingDetail(false);

    // Restart auto-rotation when hiding detail
    startAutoRotation();
  }, [startAutoRotation]);

  return (
    <div className="min-h-screen professional-gradient-bg text-base-content overflow-hidden">
      {/* Header dengan logo perusahaan */}
      <header className="w-full max-w-7xl mx-auto px-4 py-3 flex items-center justify-start">
        <div className="flex items-center gap-4">
          <img
            src="/images/icons/icon-192x192.png"
            alt="PT Putera Wibowo Borneo Logo"
            className="w-10 h-10 object-contain"
          />
          <h1 className="text-lg font-semibold text-primary tracking-wide">
            PT Putera Wibowo Borneo
          </h1>
        </div>
      </header>

      {/* Main Content Area - Responsive Grid Layout */}
      <div className="flex-1 h-[calc(100vh-80px)] relative">
        {/* Tablet Landscape Layout (1024px+) */}
        <div className="hidden lg:grid lg:grid-cols-2 h-full">
          {/* Left Section - Date/Time (50% width) */}
          <div className="flex flex-col items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20 text-base-content relative overflow-hidden">
            <div className="text-center z-10 p-8 datetime-display">
              <div className="text-6xl font-bold text-primary mb-4 datetime-time">
                {formatTime(currentDateTime)}
              </div>
              <div className="text-2xl font-medium text-base-content/80 leading-relaxed datetime-date">
                {formatDate(currentDateTime)}
              </div>
            </div>
            {/* Decorative background elements */}
            <div className="absolute top-10 left-10 w-32 h-32 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-secondary/10 rounded-full blur-lg"></div>
          </div>

          {/* Right Section - Product Carousel (50% width) */}
          <div className="relative bg-base-100">
            {/* Product Section Content */}
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefDesktop} className="carousel h-full">
                <div ref={listRefDesktop} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tablet Portrait Layout (768px - 1023px) */}
        <div className="hidden md:flex lg:hidden flex-col h-full">
          {/* Top Section - Date/Time (40% height) */}
          <div className="h-2/5 flex flex-col items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20 text-base-content relative overflow-hidden">
            <div className="text-center z-10 p-6 datetime-display">
              <div className="text-4xl font-bold text-primary mb-3 datetime-time">
                {formatTime(currentDateTime)}
              </div>
              <div className="text-lg font-medium text-base-content/80 leading-relaxed datetime-date">
                {formatDate(currentDateTime)}
              </div>
            </div>
            {/* Decorative background elements */}
            <div className="absolute top-5 left-5 w-20 h-20 bg-primary/10 rounded-full blur-lg"></div>
            <div className="absolute bottom-5 right-5 w-16 h-16 bg-secondary/10 rounded-full blur-md"></div>
          </div>

          {/* Bottom Section - Product Carousel (60% height) */}
          <div className="h-3/5 relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefTablet} className="carousel h-full">
                <div ref={listRefTablet} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout (< 768px) */}
        <div className="md:hidden h-full">
          {/* Mobile uses existing carousel layout with improved styling */}
          <div className="h-full relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefMobile} className="carousel h-full">
                <div ref={listRefMobile} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}>
                    See All &#8599;
                  </button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Homepage;
