/* Navigation Component Styles - Responsive untuk semua ukuran layar */
*{
 color: #0f2d67;
}
/* Container navigasi */
.navigation-container {
  flex-shrink: 0;
  width: 100%;
  background-color: var(--fallback-b1, oklch(var(--b1)));
  border-top: 1px solid var(--fallback-b3, oklch(var(--b3)));
}

/* Divider styling */
.navigation-container .divider {
  margin: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--fallback-b3, oklch(var(--b3))), transparent);
}

/* Navigation tabs container */
.navigation-container .tabs {
  padding: 8px 4px;
  gap: 4px;
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-container .tabs::-webkit-scrollbar {
  display: none;
}

/* Individual tab styling */
.navigation-container .tab {
  min-height: 44px;
  min-width: 60px;
  max-width: 80px;
  padding: 6px 8px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;
  background: transparent;
  color: var(--fallback-bc, oklch(var(--bc)));
}

/* Active tab styling */
.navigation-container .tab.tab-active {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Hover effects */
.navigation-container .tab:hover:not(.tab-active) {
  background: var(--fallback-b2, oklch(var(--b2)));
  color: var(--fallback-p, oklch(var(--p)));
  transform: scale(1.02);
}

/* Icon styling */
.navigation-container .tab span:first-child {
  font-size: 1.2rem;
  line-height: 1;
}

/* Label styling */
.navigation-container .tab span:last-child {
  font-size: 0.65rem;
  font-weight: 700;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design untuk Tablet 11 inch Landscape (1024px - 1366px) */
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .navigation-container .tabs {
    padding: 12px 8px;
    min-height: 70px;
    gap: 8px;
  }

  .navigation-container .tab {
    min-height: 50px;
    min-width: 80px;
    max-width: 100px;
    padding: 8px 12px;
    gap: 4px;
  }

  .navigation-container .tab span:first-child {
    font-size: 1.4rem;
  }

  .navigation-container .tab span:last-child {
    font-size: 0.7rem;
  }
}

/* Responsive Design untuk Tablet Portrait (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .navigation-container .tabs {
    padding: 10px 6px;
    min-height: 65px;
    gap: 6px;
  }

  .navigation-container .tab {
    min-height: 48px;
    min-width: 70px;
    max-width: 90px;
    padding: 7px 10px;
    gap: 3px;
  }

  .navigation-container .tab span:first-child {
    font-size: 1.3rem;
  }

  .navigation-container .tab span:last-child {
    font-size: 0.68rem;
  }
}

/* Responsive Design untuk Mobile (max-width: 767px) */
@media screen and (max-width: 767px) {
  .navigation-container .tabs {
    padding: 6px 2px;
    min-height: 60px;
    gap: 2px;
  }

  .navigation-container .tab {
    min-height: 44px;
    min-width: 50px;
    max-width: 70px;
    padding: 4px 6px;
    gap: 1px;
    font-size: 0.7rem;
  }

  .navigation-container .tab span:first-child {
    font-size: 1.1rem;
  }

  .navigation-container .tab span:last-child {
    font-size: 0.6rem;
    font-weight: 800;
  }
}

/* Extra small screens (max-width: 480px) */
@media screen and (max-width: 480px) {
  .navigation-container .tab {
    min-width: 45px;
    max-width: 60px;
    padding: 3px 4px;
  }

  .navigation-container .tab span:first-child {
    font-size: 1rem;
  }

  .navigation-container .tab span:last-child {
    font-size: 0.55rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .navigation-container .tab {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navigation-container {
    border-top: 2px solid;
  }

  .navigation-container .tab.tab-active {
    border: 2px solid;
  }
}

/* Focus states for keyboard navigation */
.navigation-container .tab:focus {
  outline: 2px solid var(--fallback-p, oklch(var(--p)));
  outline-offset: 2px;
}

.navigation-container .tab:focus:not(:focus-visible) {
  outline: none;
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .navigation-container .tab {
    min-height: 48px;
    min-width: 48px;
  }

  .navigation-container .tab:hover {
    transform: none;
  }

  .navigation-container .tab:active {
    transform: scale(0.95);
    background: var(--fallback-b3, oklch(var(--b3)));
  }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .navigation-container .tabs {
    padding: 4px 2px;
    min-height: 50px;
  }

  .navigation-container .tab {
    min-height: 40px;
    padding: 2px 4px;
    gap: 1px;
  }

  .navigation-container .tab span:first-child {
    font-size: 0.9rem;
  }

  .navigation-container .tab span:last-child {
    font-size: 0.5rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .navigation-container .tab {
    border: 0.5px solid transparent;
  }

  .navigation-container .tab.tab-active {
    border: 0.5px solid var(--fallback-p, oklch(var(--p)));
  }
}
.btnneomorphic{
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(255, 255, 255, 0.1) inset; 
  transition: all 0.2s ease;
}