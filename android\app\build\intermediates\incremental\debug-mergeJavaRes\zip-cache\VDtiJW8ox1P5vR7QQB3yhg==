[{"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCBuildConfig.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCBuildConfig.class", "size": 984, "crc": -1598332470}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$Companion.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$Companion.class", "size": 946, "crc": -1185416303}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1$1.class", "size": 5047, "crc": 1351073853}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1$3.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1$3.class", "size": 1553, "crc": 1977730327}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$addWatch$1.class", "size": 7926, "crc": 1742820848}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$checkLocationPreconditions$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$checkLocationPreconditions$1.class", "size": 2522, "crc": -1811070813}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$getCurrentPosition$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController$getCurrentPosition$1.class", "size": 2279, "crc": -1258101984}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCController.class", "size": 15787, "crc": -535355439}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper$checkLocationSettings$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper$checkLocationSettings$1.class", "size": 2044, "crc": -932654455}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper$getCurrentLocation$1.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper$getCurrentLocation$1.class", "size": 1984, "crc": 550917252}, {"key": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper.class", "name": "io/ionic/libs/iongeolocationlib/controller/IONGLOCServiceHelper.class", "size": 12729, "crc": -1148551635}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCGoogleServicesException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCGoogleServicesException.class", "size": 1696, "crc": -1078133475}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCInvalidTimeoutException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCInvalidTimeoutException.class", "size": 1505, "crc": 1228218366}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCLocationRetrievalTimeoutException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCLocationRetrievalTimeoutException.class", "size": 1535, "crc": 817405117}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCRequestDeniedException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCRequestDeniedException.class", "size": 1502, "crc": 896574787}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCSettingsException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException$IONGLOCSettingsException.class", "size": 1487, "crc": -1722675676}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCException.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCException.class", "size": 2298, "crc": 1280924106}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCLocationOptions.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCLocationOptions.class", "size": 3870, "crc": 1970043379}, {"key": "io/ionic/libs/iongeolocationlib/model/IONGLOCLocationResult.class", "name": "io/ionic/libs/iongeolocationlib/model/IONGLOCLocationResult.class", "size": 5280, "crc": -1748126718}, {"key": "META-INF/IONGeolocationLib_release.kotlin_module", "name": "META-INF/IONGeolocationLib_release.kotlin_module", "size": 24, "crc": -1111743755}]