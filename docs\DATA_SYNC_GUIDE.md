# 🔄 Data Synchronization Guide - PPWA

Panduan lengkap tentang sistem sinkronisasi data offline/online di aplikasi PPWA.

## 📋 Overview

PPWA mengimplementasikan **offline-first architecture** yang memungkinkan aplikasi berfungsi sepenuhnya tanpa koneksi internet, dengan sinkronisasi otomatis ketika koneksi tersedia.

## 🏗️ Architecture

### Components
1. **Service Worker** - Caching dan offline functionality
2. **IndexedDB** - Local database untuk data persistence
3. **Connectivity Manager** - Monitoring status koneksi
4. **Background Sync** - Automatic data synchronization

### Data Flow
```
User Action → Local Storage (IndexedDB) → Sync Queue → Background Sync → Remote API
     ↓                                                                        ↓
UI Update ← Local Data ← Cache Update ← Sync Response ← API Response
```

## 💾 Local Storage (IndexedDB)

### Database Structure
```typescript
PPWA_DB (Database)
├── preferences (ObjectStore)     // User preferences
├── products (ObjectStore)        // Product data cache
├── syncQueue (ObjectStore)       // Pending sync items
└── appState (ObjectStore)        // Application state
```

### Data Types

#### User Preferences
```typescript
interface UserPreferences {
  theme: string;           // UI theme
  language: string;        // App language
  notifications: boolean;  // Notification settings
  autoSync: boolean;       // Auto sync enabled
  syncInterval: number;    // Sync interval in minutes
}
```

#### Product Data
```typescript
interface ProductData {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: object;
  cached: boolean;         // Is data cached locally
  lastUpdated: number;     // Last update timestamp
}
```

#### Sync Queue Item
```typescript
interface StorageItem {
  id: string;              // Unique sync item ID
  data: any;               // Data to sync
  timestamp: number;       // Creation timestamp
  synced: boolean;         // Sync status
  lastModified: number;    // Last modification time
}
```

## 🔄 Synchronization Process

### 1. Offline Data Storage
Ketika user melakukan action offline:
```typescript
// Example: Save user preference
await storage.savePreferences({
  theme: 'dark',
  language: 'id',
  notifications: true,
  autoSync: true,
  syncInterval: 10
});

// Add to sync queue
await storage.addToSyncQueue({
  data: { action: 'updatePreferences', payload: preferences },
  timestamp: Date.now(),
  synced: false,
  lastModified: Date.now()
});
```

### 2. Connectivity Detection
```typescript
// Automatic detection
window.addEventListener('online', handleOnline);
window.addEventListener('offline', handleOffline);

// Manual check
const isOnline = navigator.onLine;
const status = connectivityManager.getStatus();
```

### 3. Background Sync
Sinkronisasi otomatis setiap 10 menit (configurable):
```typescript
// Start periodic sync
setInterval(async () => {
  if (connectivityManager.getStatus().isOnline) {
    await connectivityManager.performBackgroundSync();
  }
}, 10 * 60 * 1000); // 10 minutes
```

### 4. Sync Process Flow
```typescript
async function performBackgroundSync() {
  // 1. Get pending sync items
  const pendingItems = await storage.getPendingSyncItems();
  
  // 2. Sync each item
  for (const item of pendingItems) {
    try {
      await syncItem(item);
      await storage.markSyncCompleted(item.id);
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }
  
  // 3. Update cached data
  await updateCachedData();
  
  // 4. Update last sync timestamp
  await storage.saveAppState('lastSync', Date.now());
}
```

## 🌐 Service Worker Caching

### Caching Strategies

#### 1. Cache First (Static Assets)
```javascript
// CSS, JS, images, fonts
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse; // Serve from cache
  }
  
  const networkResponse = await fetch(request);
  cache.put(request, networkResponse.clone());
  return networkResponse;
}
```

#### 2. Network First (API Data)
```javascript
// API calls, dynamic data
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    const cache = await caches.open(cacheName);
    cache.put(request, networkResponse.clone());
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(cacheName);
    return await cache.match(request);
  }
}
```

#### 3. Stale While Revalidate (HTML Pages)
```javascript
// HTML pages, frequently updated content
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Update cache in background
  fetch(request).then(response => {
    cache.put(request, response.clone());
  });
  
  return cachedResponse || fetch(request);
}
```

## 📱 Connectivity Management

### Status Monitoring
```typescript
interface ConnectivityStatus {
  isOnline: boolean;        // Current online status
  lastOnline: number;       // Last online timestamp
  lastSync: number;         // Last sync timestamp
  syncInProgress: boolean;  // Sync status
  connectionType?: string;  // Connection type (wifi, cellular)
  effectiveType?: string;   // Effective connection type (4g, 3g, etc)
}
```

### Visual Indicators
```typescript
// Toast notifications
showToast('🟢 Kembali online - Sinkronisasi data...', 'success');
showToast('🔴 Offline - Menggunakan data tersimpan', 'warning');
showToast('✅ Data tersinkronisasi (5 item)', 'success');
showToast('❌ Gagal sinkronisasi data', 'error');
```

## 🔧 Configuration

### Sync Interval
```typescript
// Set sync interval (in minutes)
connectivityManager.setSyncInterval(15); // 15 minutes

// Get from user preferences
const preferences = await storage.getPreferences();
connectivityManager.setSyncInterval(preferences.syncInterval);
```

### Cache Configuration
```javascript
// Service Worker cache names
const STATIC_CACHE = 'ppwa-static-v1';
const DYNAMIC_CACHE = 'ppwa-dynamic-v1';
const DATA_CACHE = 'ppwa-data-v1';

// Cache expiration
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
```

## 🧪 Testing Sync Functionality

### 1. Offline Testing
```bash
# Chrome DevTools
1. Open DevTools (F12)
2. Go to Network tab
3. Check "Offline" checkbox
4. Test app functionality
```

### 2. Manual Sync Testing
```typescript
// Force sync
const result = await connectivityManager.forcSync();
console.log('Sync result:', result);

// Check sync status
const status = connectivityManager.getStatus();
console.log('Last sync:', connectivityManager.getTimeSinceLastSync());
```

### 3. Storage Testing
```typescript
// Check stored data
const products = await storage.getProducts();
const preferences = await storage.getPreferences();
const pendingItems = await storage.getPendingSyncItems();

console.log('Cached products:', products.length);
console.log('Pending sync items:', pendingItems.length);
```

## 🚨 Error Handling

### Sync Errors
```typescript
interface SyncResult {
  success: boolean;
  itemsSynced: number;
  errors: string[];
  timestamp: number;
}

// Handle sync errors
if (!result.success) {
  console.error('Sync failed:', result.errors);
  // Show user notification
  showToast('❌ Gagal sinkronisasi: ' + result.errors[0], 'error');
}
```

### Storage Errors
```typescript
try {
  await storage.savePreferences(preferences);
} catch (error) {
  console.error('Storage error:', error);
  // Fallback to localStorage
  localStorage.setItem('preferences', JSON.stringify(preferences));
}
```

### Network Errors
```typescript
// Retry mechanism
async function syncWithRetry(item, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await syncItem(item);
      return;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 📊 Performance Optimization

### 1. Batch Sync
```typescript
// Sync multiple items in batches
const BATCH_SIZE = 10;
const batches = chunk(pendingItems, BATCH_SIZE);

for (const batch of batches) {
  await Promise.all(batch.map(item => syncItem(item)));
}
```

### 2. Selective Sync
```typescript
// Only sync changed data
const changedItems = pendingItems.filter(item => 
  item.lastModified > lastSyncTime
);
```

### 3. Compression
```typescript
// Compress large data before storage
const compressedData = LZString.compress(JSON.stringify(data));
await storage.saveAppState('largeData', compressedData);
```

## 🔍 Monitoring & Analytics

### Sync Metrics
```typescript
// Track sync performance
const syncMetrics = {
  totalSyncs: 0,
  successfulSyncs: 0,
  failedSyncs: 0,
  averageSyncTime: 0,
  lastSyncDuration: 0
};

// Update metrics after each sync
updateSyncMetrics(result);
```

### Storage Usage
```typescript
// Monitor storage usage
navigator.storage.estimate().then(estimate => {
  console.log('Storage used:', estimate.usage);
  console.log('Storage quota:', estimate.quota);
  console.log('Usage percentage:', (estimate.usage / estimate.quota) * 100);
});
```

## 🛠️ Troubleshooting

### Common Issues

1. **Sync Not Working**
   - Check internet connection
   - Verify service worker registration
   - Check browser console for errors

2. **Data Not Persisting**
   - Check IndexedDB support
   - Verify storage permissions
   - Check available storage space

3. **Performance Issues**
   - Reduce sync frequency
   - Implement data pagination
   - Clear old cached data

### Debug Tools
```typescript
// Enable debug logging
localStorage.setItem('ppwa-debug', 'true');

// View storage data
await storage.getAppState('debug-info');

// Clear all data (reset)
await storage.clearAll();
```

---

**📞 Support**: Untuk pertanyaan teknis, lihat [troubleshooting guide](./TROUBLESHOOTING.md) atau hubungi tim development.
