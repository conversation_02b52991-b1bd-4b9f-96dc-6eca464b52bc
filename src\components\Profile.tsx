import React, { useState, useEffect } from 'react';
import { Geolocation } from '@capacitor/geolocation';
import { Device } from '@capacitor/device';
import { App } from '@capacitor/app';
import { connectivityManager } from '../utils/connectivity';
import type { ConnectivityStatus } from '../utils/connectivity';
import { storage } from '../utils/storage';

/**
 * Interface untuk device information
 */
interface DeviceInfo {
  uuid: string;
  platform: string;
  model: string;
  operatingSystem: string;
  osVersion: string;
  manufacturer: string;
  isVirtual: boolean;
  webViewVersion: string;
}

/**
 * Interface untuk location information
 */
interface LocationInfo {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

/**
 * Interface untuk app information
 */
interface AppInfo {
  name: string;
  id: string;
  build: string;
  version: string;
}

/**
 * Komponen Profile dengan informasi device dan lokasi
 */
const Profile: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null);
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [connectivityStatus, setConnectivityStatus] = useState<ConnectivityStatus>(
    connectivityManager.getStatus()
  );
  const [locationError, setLocationError] = useState<string>('');
  const [locationLoading, setLocationLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<{
    location: string;
    granted: boolean;
  }>({ location: 'prompt', granted: false });

  useEffect(() => {
    initializeProfile();
    
    // Listen to connectivity changes
    const handleConnectivityChange = (status: ConnectivityStatus) => {
      setConnectivityStatus(status);
    };
    
    connectivityManager.addListener(handleConnectivityChange);
    
    return () => {
      connectivityManager.removeListener(handleConnectivityChange);
    };
  }, []);

  /**
   * Initialize profile data
   */
  const initializeProfile = async () => {
    try {
      // Get device information
      await getDeviceInfo();
      
      // Get app information
      await getAppInfo();
      
      // Check location permission
      await checkLocationPermission();
      
    } catch (error) {
      console.error('[Profile] Error initializing profile:', error);
    }
  };

  /**
   * Get device information using Capacitor Device plugin
   */
  const getDeviceInfo = async () => {
    try {
      const info = await Device.getInfo();
      const deviceId = await Device.getId();
      
      setDeviceInfo({
        uuid: deviceId.identifier,
        platform: info.platform,
        model: info.model,
        operatingSystem: info.operatingSystem,
        osVersion: info.osVersion,
        manufacturer: info.manufacturer,
        isVirtual: info.isVirtual,
        webViewVersion: info.webViewVersion
      });
      
      console.log('[Profile] Device info loaded:', info);
    } catch (error) {
      console.error('[Profile] Error getting device info:', error);
      // Fallback for web
      setDeviceInfo({
        uuid: 'web-' + Math.random().toString(36).substr(2, 9),
        platform: 'web',
        model: navigator.userAgent,
        operatingSystem: navigator.platform,
        osVersion: 'Unknown',
        manufacturer: 'Unknown',
        isVirtual: false,
        webViewVersion: 'Unknown'
      });
    }
  };

  /**
   * Get app information using Capacitor App plugin
   */
  const getAppInfo = async () => {
    try {
      const info = await App.getInfo();
      setAppInfo(info);
      console.log('[Profile] App info loaded:', info);
    } catch (error) {
      console.error('[Profile] Error getting app info:', error);
      // Fallback for web
      setAppInfo({
        name: 'PPWA',
        id: 'com.ppwa.app',
        build: '1',
        version: '1.0.0'
      });
    }
  };

  /**
   * Check location permission status
   */
  const checkLocationPermission = async () => {
    try {
      const permission = await Geolocation.checkPermissions();
      setPermissionStatus({
        location: permission.location,
        granted: permission.location === 'granted'
      });
      
      // If permission is granted, get location automatically
      if (permission.location === 'granted') {
        await getCurrentLocation();
      }
    } catch (error) {
      console.error('[Profile] Error checking location permission:', error);
    }
  };

  /**
   * Request location permission
   */
  const requestLocationPermission = async () => {
    try {
      const permission = await Geolocation.requestPermissions();
      setPermissionStatus({
        location: permission.location,
        granted: permission.location === 'granted'
      });
      
      if (permission.location === 'granted') {
        await getCurrentLocation();
      } else {
        setLocationError('Izin lokasi ditolak. Silakan aktifkan di pengaturan aplikasi.');
      }
    } catch (error) {
      console.error('[Profile] Error requesting location permission:', error);
      setLocationError('Gagal meminta izin lokasi: ' + error);
    }
  };

  /**
   * Get current location using Capacitor Geolocation plugin
   */
  const getCurrentLocation = async () => {
    setLocationLoading(true);
    setLocationError('');
    
    try {
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000
      });
      
      const locationData: LocationInfo = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        altitude: position.coords.altitude || undefined,
        altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
        heading: position.coords.heading || undefined,
        speed: position.coords.speed || undefined,
        timestamp: position.timestamp
      };
      
      setLocationInfo(locationData);
      
      // Save location to storage
      await storage.saveAppState('lastLocation', locationData);
      
      console.log('[Profile] Location obtained:', locationData);
    } catch (error) {
      console.error('[Profile] Error getting location:', error);
      setLocationError('Gagal mendapatkan lokasi: ' + error);
      
      // Try to load last known location
      try {
        const lastLocation = await storage.getAppState('lastLocation');
        if (lastLocation) {
          setLocationInfo(lastLocation);
          setLocationError('Menggunakan lokasi terakhir yang diketahui');
        }
      } catch (storageError) {
        console.error('[Profile] Error loading last location:', storageError);
      }
    } finally {
      setLocationLoading(false);
    }
  };

  /**
   * Format coordinates for display
   */
  const formatCoordinates = (lat: number, lng: number): string => {
    const latDir = lat >= 0 ? 'N' : 'S';
    const lngDir = lng >= 0 ? 'E' : 'W';
    
    return `${Math.abs(lat).toFixed(6)}°${latDir}, ${Math.abs(lng).toFixed(6)}°${lngDir}`;
  };

  /**
   * Format timestamp to readable date
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  /**
   * Force sync data
   */
  const handleForceSync = async () => {
    try {
      const result = await connectivityManager.forcSync();
      console.log('[Profile] Force sync result:', result);
    } catch (error) {
      console.error('[Profile] Force sync error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-base-100 p-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-primary mb-2">👤 Profile</h1>
          <p className="text-lg text-base-content opacity-70">
            Informasi Device dan Lokasi
          </p>
        </div>

        {/* Connectivity Status */}
        <div className="card bg-base-200 shadow-xl mb-6">
          <div className="card-body">
            <h2 className="card-title flex items-center">
              <span className={`w-3 h-3 rounded-full ${connectivityStatus.isOnline ? 'bg-success' : 'bg-error'}`}></span>
              Status Koneksi
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>Status:</strong> {connectivityStatus.isOnline ? '🟢 Online' : '🔴 Offline'}</p>
                <p><strong>Sinkronisasi Terakhir:</strong> {connectivityManager.getTimeSinceLastSync()}</p>
              </div>
              <div>
                <p><strong>Tipe Koneksi:</strong> {connectivityStatus.connectionType || 'Unknown'}</p>
                <p><strong>Kecepatan:</strong> {connectivityStatus.effectiveType || 'Unknown'}</p>
              </div>
            </div>
            <div className="card-actions justify-end mt-4">
              <button 
                className="btn btn-primary btn-sm"
                onClick={handleForceSync}
                disabled={!connectivityStatus.isOnline || connectivityStatus.syncInProgress}
              >
                {connectivityStatus.syncInProgress ? 'Sinkronisasi...' : 'Paksa Sinkronisasi'}
              </button>
            </div>
          </div>
        </div>

        {/* Device Information */}
        <div className="card bg-base-200 shadow-xl mb-6">
          <div className="card-body">
            <h2 className="card-title">📱 Informasi Device</h2>
            {deviceInfo ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>Device ID:</strong> <code className="text-xs bg-base-300 px-2 py-1 rounded">{deviceInfo.uuid}</code></p>
                  <p><strong>Platform:</strong> {deviceInfo.platform}</p>
                  <p><strong>Model:</strong> {deviceInfo.model}</p>
                  <p><strong>Manufacturer:</strong> {deviceInfo.manufacturer}</p>
                </div>
                <div>
                  <p><strong>OS:</strong> {deviceInfo.operatingSystem} {deviceInfo.osVersion}</p>
                  <p><strong>WebView:</strong> {deviceInfo.webViewVersion}</p>
                  <p><strong>Virtual Device:</strong> {deviceInfo.isVirtual ? 'Ya' : 'Tidak'}</p>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <span className="loading loading-spinner loading-md"></span>
              </div>
            )}
          </div>
        </div>

        {/* App Information */}
        <div className="card bg-base-200 shadow-xl mb-6">
          <div className="card-body">
            <h2 className="card-title">📦 Informasi Aplikasi</h2>
            {appInfo ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>Nama:</strong> {appInfo.name}</p>
                  <p><strong>ID:</strong> {appInfo.id}</p>
                </div>
                <div>
                  <p><strong>Versi:</strong> {appInfo.version}</p>
                  <p><strong>Build:</strong> {appInfo.build}</p>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <span className="loading loading-spinner loading-md"></span>
              </div>
            )}
          </div>
        </div>

        {/* Location Information */}
        <div className="card bg-base-200 shadow-xl mb-6">
          <div className="card-body">
            <h2 className="card-title">📍 Informasi Lokasi</h2>
            
            {!permissionStatus.granted ? (
              <div className="text-center py-4">
                <p className="mb-4">Izin lokasi diperlukan untuk menampilkan koordinat device</p>
                <button 
                  className="btn btn-primary"
                  onClick={requestLocationPermission}
                >
                  Minta Izin Lokasi
                </button>
              </div>
            ) : locationInfo ? (
              <div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p><strong>Koordinat:</strong> {formatCoordinates(locationInfo.latitude, locationInfo.longitude)}</p>
                    <p><strong>Latitude:</strong> {locationInfo.latitude.toFixed(6)}</p>
                    <p><strong>Longitude:</strong> {locationInfo.longitude.toFixed(6)}</p>
                    <p><strong>Akurasi:</strong> ±{locationInfo.accuracy.toFixed(0)}m</p>
                  </div>
                  <div>
                    {locationInfo.altitude && <p><strong>Altitude:</strong> {locationInfo.altitude.toFixed(0)}m</p>}
                    {locationInfo.speed && <p><strong>Kecepatan:</strong> {(locationInfo.speed * 3.6).toFixed(1)} km/h</p>}
                    {locationInfo.heading && <p><strong>Arah:</strong> {locationInfo.heading.toFixed(0)}°</p>}
                    <p><strong>Waktu:</strong> {formatTimestamp(locationInfo.timestamp)}</p>
                  </div>
                </div>
                <div className="card-actions justify-end">
                  <button 
                    className="btn btn-outline btn-sm"
                    onClick={getCurrentLocation}
                    disabled={locationLoading}
                  >
                    {locationLoading ? 'Memperbarui...' : 'Perbarui Lokasi'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                {locationLoading ? (
                  <div>
                    <span className="loading loading-spinner loading-md"></span>
                    <p className="mt-2">Mendapatkan lokasi...</p>
                  </div>
                ) : (
                  <div>
                    <p className="mb-4">Belum ada data lokasi</p>
                    <button 
                      className="btn btn-primary"
                      onClick={getCurrentLocation}
                    >
                      Dapatkan Lokasi
                    </button>
                  </div>
                )}
              </div>
            )}
            
            {locationError && (
              <div className="alert alert-warning mt-4">
                <span>⚠️ {locationError}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
