[{"key": "androidx/webkit/CookieManagerCompat.class", "name": "androidx/webkit/CookieManagerCompat.class", "size": 1812, "crc": 810362068}, {"key": "androidx/webkit/DropDataContentProvider.class", "name": "androidx/webkit/DropDataContentProvider.class", "size": 3309, "crc": **********}, {"key": "androidx/webkit/JavaScriptReplyProxy.class", "name": "androidx/webkit/JavaScriptReplyProxy.class", "size": 784, "crc": **********}, {"key": "androidx/webkit/ProcessGlobalConfig.class", "name": "androidx/webkit/ProcessGlobalConfig.class", "size": 5769, "crc": **********}, {"key": "androidx/webkit/Profile.class", "name": "androidx/webkit/Profile.class", "size": 697, "crc": **********}, {"key": "androidx/webkit/ProfileStore.class", "name": "androidx/webkit/ProfileStore.class", "size": 1323, "crc": -470098592}, {"key": "androidx/webkit/ProxyConfig$Builder.class", "name": "androidx/webkit/ProxyConfig$Builder.class", "size": 3176, "crc": **********}, {"key": "androidx/webkit/ProxyConfig$ProxyRule.class", "name": "androidx/webkit/ProxyConfig$ProxyRule.class", "size": 1251, "crc": -**********}, {"key": "androidx/webkit/ProxyConfig$ProxyScheme.class", "name": "androidx/webkit/ProxyConfig$ProxyScheme.class", "size": 620, "crc": 1155789592}, {"key": "androidx/webkit/ProxyConfig.class", "name": "androidx/webkit/ProxyConfig.class", "size": 2217, "crc": -944262389}, {"key": "androidx/webkit/ProxyController$LAZY_HOLDER.class", "name": "androidx/webkit/ProxyController$LAZY_HOLDER.class", "size": 588, "crc": 475624606}, {"key": "androidx/webkit/ProxyController.class", "name": "androidx/webkit/ProxyController.class", "size": 1398, "crc": -1341257352}, {"key": "androidx/webkit/SafeBrowsingResponseCompat.class", "name": "androidx/webkit/SafeBrowsingResponseCompat.class", "size": 677, "crc": -941489355}, {"key": "androidx/webkit/ScriptHandler.class", "name": "androidx/webkit/ScriptHandler.class", "size": 221, "crc": 231112901}, {"key": "androidx/webkit/ServiceWorkerClientCompat.class", "name": "androidx/webkit/ServiceWorkerClientCompat.class", "size": 641, "crc": 1279382208}, {"key": "androidx/webkit/ServiceWorkerControllerCompat$LAZY_HOLDER.class", "name": "androidx/webkit/ServiceWorkerControllerCompat$LAZY_HOLDER.class", "size": 666, "crc": 951864278}, {"key": "androidx/webkit/ServiceWorkerControllerCompat.class", "name": "androidx/webkit/ServiceWorkerControllerCompat.class", "size": 1181, "crc": 352406528}, {"key": "androidx/webkit/ServiceWorkerWebSettingsCompat$CacheMode.class", "name": "androidx/webkit/ServiceWorkerWebSettingsCompat$CacheMode.class", "size": 673, "crc": 1632058964}, {"key": "androidx/webkit/ServiceWorkerWebSettingsCompat.class", "name": "androidx/webkit/ServiceWorkerWebSettingsCompat.class", "size": 1336, "crc": -254304286}, {"key": "androidx/webkit/TracingConfig$Builder.class", "name": "androidx/webkit/TracingConfig$Builder.class", "size": 2122, "crc": 463388108}, {"key": "androidx/webkit/TracingConfig$PredefinedCategories.class", "name": "androidx/webkit/TracingConfig$PredefinedCategories.class", "size": 644, "crc": -558306872}, {"key": "androidx/webkit/TracingConfig$TracingMode.class", "name": "androidx/webkit/TracingConfig$TracingMode.class", "size": 626, "crc": -641013872}, {"key": "androidx/webkit/TracingConfig.class", "name": "androidx/webkit/TracingConfig.class", "size": 2270, "crc": 802236340}, {"key": "androidx/webkit/TracingController$LAZY_HOLDER.class", "name": "androidx/webkit/TracingController$LAZY_HOLDER.class", "size": 600, "crc": -1372610114}, {"key": "androidx/webkit/TracingController.class", "name": "androidx/webkit/TracingController.class", "size": 1108, "crc": 605009767}, {"key": "androidx/webkit/URLUtilCompat.class", "name": "androidx/webkit/URLUtilCompat.class", "size": 5733, "crc": -1349698283}, {"key": "androidx/webkit/UserAgentMetadata$1.class", "name": "androidx/webkit/UserAgentMetadata$1.class", "size": 231, "crc": 1050557160}, {"key": "androidx/webkit/UserAgentMetadata$BrandVersion$Builder.class", "name": "androidx/webkit/UserAgentMetadata$BrandVersion$Builder.class", "size": 2432, "crc": -806185877}, {"key": "androidx/webkit/UserAgentMetadata$BrandVersion.class", "name": "androidx/webkit/UserAgentMetadata$BrandVersion.class", "size": 2436, "crc": 1319653373}, {"key": "androidx/webkit/UserAgentMetadata$Builder.class", "name": "androidx/webkit/UserAgentMetadata$Builder.class", "size": 3959, "crc": 209625139}, {"key": "androidx/webkit/UserAgentMetadata.class", "name": "androidx/webkit/UserAgentMetadata.class", "size": 4117, "crc": 411793426}, {"key": "androidx/webkit/WebMessageCompat$Type.class", "name": "androidx/webkit/WebMessageCompat$Type.class", "size": 627, "crc": -1487855087}, {"key": "androidx/webkit/WebMessageCompat.class", "name": "androidx/webkit/WebMessageCompat.class", "size": 2637, "crc": 997012634}, {"key": "androidx/webkit/WebMessagePortCompat$WebMessageCallbackCompat.class", "name": "androidx/webkit/WebMessagePortCompat$WebMessageCallbackCompat.class", "size": 852, "crc": 406953940}, {"key": "androidx/webkit/WebMessagePortCompat.class", "name": "androidx/webkit/WebMessagePortCompat.class", "size": 1391, "crc": -1363492463}, {"key": "androidx/webkit/WebResourceErrorCompat$NetErrorCode.class", "name": "androidx/webkit/WebResourceErrorCompat$NetErrorCode.class", "size": 655, "crc": 1265299041}, {"key": "androidx/webkit/WebResourceErrorCompat.class", "name": "androidx/webkit/WebResourceErrorCompat.class", "size": 797, "crc": -213705750}, {"key": "androidx/webkit/WebResourceRequestCompat.class", "name": "androidx/webkit/WebResourceRequestCompat.class", "size": 1632, "crc": -723027777}, {"key": "androidx/webkit/WebSettingsCompat$ExperimentalBackForwardCache.class", "name": "androidx/webkit/WebSettingsCompat$ExperimentalBackForwardCache.class", "size": 803, "crc": -94002531}, {"key": "androidx/webkit/WebSettingsCompat$ExperimentalSpeculativeLoading.class", "name": "androidx/webkit/WebSettingsCompat$ExperimentalSpeculativeLoading.class", "size": 807, "crc": -1265787740}, {"key": "androidx/webkit/WebSettingsCompat$ForceDark.class", "name": "androidx/webkit/WebSettingsCompat$ForceDark.class", "size": 743, "crc": -1370511495}, {"key": "androidx/webkit/WebSettingsCompat$ForceDarkStrategy.class", "name": "androidx/webkit/WebSettingsCompat$ForceDarkStrategy.class", "size": 759, "crc": -937473684}, {"key": "androidx/webkit/WebSettingsCompat$MenuItemFlags.class", "name": "androidx/webkit/WebSettingsCompat$MenuItemFlags.class", "size": 751, "crc": -991363901}, {"key": "androidx/webkit/WebSettingsCompat.class", "name": "androidx/webkit/WebSettingsCompat.class", "size": 11483, "crc": 834924183}, {"key": "androidx/webkit/WebViewAssetLoader$AssetsPathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$AssetsPathHandler.class", "size": 2107, "crc": -521037840}, {"key": "androidx/webkit/WebViewAssetLoader$Builder.class", "name": "androidx/webkit/WebViewAssetLoader$Builder.class", "size": 2823, "crc": 1969495996}, {"key": "androidx/webkit/WebViewAssetLoader$InternalStoragePathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$InternalStoragePathHandler.class", "size": 3847, "crc": -1270641844}, {"key": "androidx/webkit/WebViewAssetLoader$PathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$PathHandler.class", "size": 506, "crc": 130234646}, {"key": "androidx/webkit/WebViewAssetLoader$PathMatcher.class", "name": "androidx/webkit/WebViewAssetLoader$PathMatcher.class", "size": 2434, "crc": -290461727}, {"key": "androidx/webkit/WebViewAssetLoader$ResourcesPathHandler.class", "name": "androidx/webkit/WebViewAssetLoader$ResourcesPathHandler.class", "size": 2404, "crc": -1924473717}, {"key": "androidx/webkit/WebViewAssetLoader.class", "name": "androidx/webkit/WebViewAssetLoader.class", "size": 2597, "crc": -175094866}, {"key": "androidx/webkit/WebViewClientCompat$SafeBrowsingThreat.class", "name": "androidx/webkit/WebViewClientCompat$SafeBrowsingThreat.class", "size": 658, "crc": 870297702}, {"key": "androidx/webkit/WebViewClientCompat.class", "name": "androidx/webkit/WebViewClientCompat.class", "size": 5797, "crc": -806347364}, {"key": "androidx/webkit/WebViewCompat$VisualStateCallback.class", "name": "androidx/webkit/WebViewCompat$VisualStateCallback.class", "size": 334, "crc": 1017164834}, {"key": "androidx/webkit/WebViewCompat$WebMessageListener.class", "name": "androidx/webkit/WebViewCompat$WebMessageListener.class", "size": 552, "crc": 361360058}, {"key": "androidx/webkit/WebViewCompat.class", "name": "androidx/webkit/WebViewCompat.class", "size": 17535, "crc": -1903414813}, {"key": "androidx/webkit/WebViewFeature$WebViewStartupFeature.class", "name": "androidx/webkit/WebViewFeature$WebViewStartupFeature.class", "size": 758, "crc": 539797687}, {"key": "androidx/webkit/WebViewFeature$WebViewSupportFeature.class", "name": "androidx/webkit/WebViewFeature$WebViewSupportFeature.class", "size": 758, "crc": -750036063}, {"key": "androidx/webkit/WebViewFeature.class", "name": "androidx/webkit/WebViewFeature.class", "size": 4323, "crc": -152998436}, {"key": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig$Builder.class", "name": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig$Builder.class", "size": 2397, "crc": 1988671879}, {"key": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig.class", "name": "androidx/webkit/WebViewMediaIntegrityApiStatusConfig.class", "size": 1820, "crc": -121092107}, {"key": "androidx/webkit/WebViewRenderProcess.class", "name": "androidx/webkit/WebViewRenderProcess.class", "size": 347, "crc": -253149553}, {"key": "androidx/webkit/WebViewRenderProcessClient.class", "name": "androidx/webkit/WebViewRenderProcessClient.class", "size": 619, "crc": 1003479243}, {"key": "androidx/webkit/internal/ApiFeature$LAZY_HOLDER.class", "name": "androidx/webkit/internal/ApiFeature$LAZY_HOLDER.class", "size": 992, "crc": -570236664}, {"key": "androidx/webkit/internal/ApiFeature$M.class", "name": "androidx/webkit/internal/ApiFeature$M.class", "size": 862, "crc": 1985169946}, {"key": "androidx/webkit/internal/ApiFeature$N.class", "name": "androidx/webkit/internal/ApiFeature$N.class", "size": 862, "crc": -48645686}, {"key": "androidx/webkit/internal/ApiFeature$NoFramework.class", "name": "androidx/webkit/internal/ApiFeature$NoFramework.class", "size": 699, "crc": -1327354942}, {"key": "androidx/webkit/internal/ApiFeature$O.class", "name": "androidx/webkit/internal/ApiFeature$O.class", "size": 862, "crc": -14586643}, {"key": "androidx/webkit/internal/ApiFeature$O_MR1.class", "name": "androidx/webkit/internal/ApiFeature$O_MR1.class", "size": 874, "crc": 1471782146}, {"key": "androidx/webkit/internal/ApiFeature$P.class", "name": "androidx/webkit/internal/ApiFeature$P.class", "size": 862, "crc": -1988946976}, {"key": "androidx/webkit/internal/ApiFeature$Q.class", "name": "androidx/webkit/internal/ApiFeature$Q.class", "size": 862, "crc": -1024700076}, {"key": "androidx/webkit/internal/ApiFeature$T.class", "name": "androidx/webkit/internal/ApiFeature$T.class", "size": 862, "crc": 1244880336}, {"key": "androidx/webkit/internal/ApiFeature.class", "name": "androidx/webkit/internal/ApiFeature.class", "size": 2562, "crc": 1773078452}, {"key": "androidx/webkit/internal/ApiHelperForM$1.class", "name": "androidx/webkit/internal/ApiHelperForM$1.class", "size": 1515, "crc": -949973599}, {"key": "androidx/webkit/internal/ApiHelperForM$2.class", "name": "androidx/webkit/internal/ApiHelperForM$2.class", "size": 1535, "crc": -394847221}, {"key": "androidx/webkit/internal/ApiHelperForM$3.class", "name": "androidx/webkit/internal/ApiHelperForM$3.class", "size": 984, "crc": -298214985}, {"key": "androidx/webkit/internal/ApiHelperForM.class", "name": "androidx/webkit/internal/ApiHelperForM.class", "size": 5279, "crc": -846412684}, {"key": "androidx/webkit/internal/ApiHelperForN.class", "name": "androidx/webkit/internal/ApiHelperForN.class", "size": 4334, "crc": -876086929}, {"key": "androidx/webkit/internal/ApiHelperForO.class", "name": "androidx/webkit/internal/ApiHelperForO.class", "size": 1533, "crc": 1502295577}, {"key": "androidx/webkit/internal/ApiHelperForOMR1.class", "name": "androidx/webkit/internal/ApiHelperForOMR1.class", "size": 2044, "crc": -2088085019}, {"key": "androidx/webkit/internal/ApiHelperForP.class", "name": "androidx/webkit/internal/ApiHelperForP.class", "size": 2721, "crc": 662876331}, {"key": "androidx/webkit/internal/ApiHelperForQ.class", "name": "androidx/webkit/internal/ApiHelperForQ.class", "size": 2795, "crc": 1960351447}, {"key": "androidx/webkit/internal/ApiHelperForTiramisu.class", "name": "androidx/webkit/internal/ApiHelperForTiramisu.class", "size": 1395, "crc": -1859068649}, {"key": "androidx/webkit/internal/AssetHelper.class", "name": "androidx/webkit/internal/AssetHelper.class", "size": 5617, "crc": -891613487}, {"key": "androidx/webkit/internal/ConditionallySupportedFeature.class", "name": "androidx/webkit/internal/ConditionallySupportedFeature.class", "size": 320, "crc": **********}, {"key": "androidx/webkit/internal/CookieManagerAdapter.class", "name": "androidx/webkit/internal/CookieManagerAdapter.class", "size": 1036, "crc": -821754326}, {"key": "androidx/webkit/internal/FrameworkServiceWorkerClient.class", "name": "androidx/webkit/internal/FrameworkServiceWorkerClient.class", "size": 1047, "crc": 454232551}, {"key": "androidx/webkit/internal/IncompatibleApkWebViewProviderFactory.class", "name": "androidx/webkit/internal/IncompatibleApkWebViewProviderFactory.class", "size": 2701, "crc": -637465948}, {"key": "androidx/webkit/internal/JavaScriptReplyProxyImpl.class", "name": "androidx/webkit/internal/JavaScriptReplyProxyImpl.class", "size": 3435, "crc": 501399021}, {"key": "androidx/webkit/internal/MimeUtil.class", "name": "androidx/webkit/internal/MimeUtil.class", "size": 3763, "crc": **********}, {"key": "androidx/webkit/internal/ProfileImpl.class", "name": "androidx/webkit/internal/ProfileImpl.class", "size": 2258, "crc": 51677491}, {"key": "androidx/webkit/internal/ProfileStoreImpl.class", "name": "androidx/webkit/internal/ProfileStoreImpl.class", "size": 3321, "crc": -758074676}, {"key": "androidx/webkit/internal/ProxyControllerImpl.class", "name": "androidx/webkit/internal/ProxyControllerImpl.class", "size": 3818, "crc": 80607241}, {"key": "androidx/webkit/internal/SafeBrowsingResponseImpl.class", "name": "androidx/webkit/internal/SafeBrowsingResponseImpl.class", "size": 3510, "crc": -394302484}, {"key": "androidx/webkit/internal/ScriptHandlerImpl.class", "name": "androidx/webkit/internal/ScriptHandlerImpl.class", "size": 1335, "crc": 1397381064}, {"key": "androidx/webkit/internal/ServiceWorkerClientAdapter.class", "name": "androidx/webkit/internal/ServiceWorkerClientAdapter.class", "size": 1284, "crc": 1015890022}, {"key": "androidx/webkit/internal/ServiceWorkerControllerImpl.class", "name": "androidx/webkit/internal/ServiceWorkerControllerImpl.class", "size": 3562, "crc": 1434875371}, {"key": "androidx/webkit/internal/ServiceWorkerWebSettingsImpl.class", "name": "androidx/webkit/internal/ServiceWorkerWebSettingsImpl.class", "size": 5488, "crc": -1869283298}, {"key": "androidx/webkit/internal/StartupApiFeature$NoFramework.class", "name": "androidx/webkit/internal/StartupApiFeature$NoFramework.class", "size": 727, "crc": -15887176}, {"key": "androidx/webkit/internal/StartupApiFeature$P.class", "name": "androidx/webkit/internal/StartupApiFeature$P.class", "size": 890, "crc": -1822135052}, {"key": "androidx/webkit/internal/StartupApiFeature.class", "name": "androidx/webkit/internal/StartupApiFeature.class", "size": 4544, "crc": -950453910}, {"key": "androidx/webkit/internal/StartupFeatures.class", "name": "androidx/webkit/internal/StartupFeatures.class", "size": 485, "crc": 860068260}, {"key": "androidx/webkit/internal/TracingControllerImpl.class", "name": "androidx/webkit/internal/TracingControllerImpl.class", "size": 3427, "crc": 58053586}, {"key": "androidx/webkit/internal/UserAgentMetadataInternal.class", "name": "androidx/webkit/internal/UserAgentMetadataInternal.class", "size": 5218, "crc": 537945338}, {"key": "androidx/webkit/internal/VisualStateCallbackAdapter.class", "name": "androidx/webkit/internal/VisualStateCallbackAdapter.class", "size": 982, "crc": 201325726}, {"key": "androidx/webkit/internal/WebMessageAdapter.class", "name": "androidx/webkit/internal/WebMessageAdapter.class", "size": 4751, "crc": 554112275}, {"key": "androidx/webkit/internal/WebMessageCallbackAdapter.class", "name": "androidx/webkit/internal/WebMessageCallbackAdapter.class", "size": 2206, "crc": -1727309226}, {"key": "androidx/webkit/internal/WebMessageListenerAdapter.class", "name": "androidx/webkit/internal/WebMessageListenerAdapter.class", "size": 2591, "crc": 1855951151}, {"key": "androidx/webkit/internal/WebMessagePayloadAdapter.class", "name": "androidx/webkit/internal/WebMessagePayloadAdapter.class", "size": 2091, "crc": 65848807}, {"key": "androidx/webkit/internal/WebMessagePortImpl.class", "name": "androidx/webkit/internal/WebMessagePortImpl.class", "size": 6206, "crc": -800396763}, {"key": "androidx/webkit/internal/WebResourceErrorImpl.class", "name": "androidx/webkit/internal/WebResourceErrorImpl.class", "size": 3205, "crc": 1054619571}, {"key": "androidx/webkit/internal/WebResourceRequestAdapter.class", "name": "androidx/webkit/internal/WebResourceRequestAdapter.class", "size": 837, "crc": -772085548}, {"key": "androidx/webkit/internal/WebSettingsAdapter.class", "name": "androidx/webkit/internal/WebSettingsAdapter.class", "size": 5798, "crc": 687167600}, {"key": "androidx/webkit/internal/WebViewFeatureInternal$1.class", "name": "androidx/webkit/internal/WebViewFeatureInternal$1.class", "size": 1832, "crc": -785126732}, {"key": "androidx/webkit/internal/WebViewFeatureInternal$2.class", "name": "androidx/webkit/internal/WebViewFeatureInternal$2.class", "size": 1013, "crc": -793800528}, {"key": "androidx/webkit/internal/WebViewFeatureInternal.class", "name": "androidx/webkit/internal/WebViewFeatureInternal.class", "size": 9970, "crc": 1272423626}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_COMPAT_CONVERTER_HOLDER.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_COMPAT_CONVERTER_HOLDER.class", "size": 1072, "crc": **********}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_FACTORY_HOLDER.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator$LAZY_FACTORY_HOLDER.class", "size": 724, "crc": -33542635}, {"key": "androidx/webkit/internal/WebViewGlueCommunicator.class", "name": "androidx/webkit/internal/WebViewGlueCommunicator.class", "size": 3937, "crc": -**********}, {"key": "androidx/webkit/internal/WebViewProviderAdapter.class", "name": "androidx/webkit/internal/WebViewProviderAdapter.class", "size": 6638, "crc": -**********}, {"key": "androidx/webkit/internal/WebViewProviderFactory.class", "name": "androidx/webkit/internal/WebViewProviderFactory.class", "size": 1283, "crc": 885493217}, {"key": "androidx/webkit/internal/WebViewProviderFactoryAdapter.class", "name": "androidx/webkit/internal/WebViewProviderFactoryAdapter.class", "size": 3551, "crc": -604233747}, {"key": "androidx/webkit/internal/WebViewRenderProcessClientAdapter.class", "name": "androidx/webkit/internal/WebViewRenderProcessClientAdapter.class", "size": 3676, "crc": **********}, {"key": "androidx/webkit/internal/WebViewRenderProcessClientFrameworkAdapter.class", "name": "androidx/webkit/internal/WebViewRenderProcessClientFrameworkAdapter.class", "size": 1737, "crc": -**********}, {"key": "androidx/webkit/internal/WebViewRenderProcessImpl.class", "name": "androidx/webkit/internal/WebViewRenderProcessImpl.class", "size": 4099, "crc": **********}, {"key": "androidx/webkit/internal/WebkitToCompatConverter.class", "name": "androidx/webkit/internal/WebkitToCompatConverter.class", "size": 4886, "crc": -**********}, {"key": "androidx/webkit/internal/package-info.class", "name": "androidx/webkit/internal/package-info.class", "size": 393, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/DropDataContentProviderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/DropDataContentProviderBoundaryInterface.class", "size": 1124, "crc": 87374064}, {"key": "org/chromium/support_lib_boundary/FeatureFlagHolderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/FeatureFlagHolderBoundaryInterface.class", "size": 238, "crc": -618081985}, {"key": "org/chromium/support_lib_boundary/IsomorphicObjectBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/IsomorphicObjectBoundaryInterface.class", "size": 355, "crc": -189251523}, {"key": "org/chromium/support_lib_boundary/JsReplyProxyBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/JsReplyProxyBoundaryInterface.class", "size": 370, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/PrefetchOperationResultBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/PrefetchOperationResultBoundaryInterface.class", "size": 225, "crc": **********}, {"key": "org/chromium/support_lib_boundary/PrefetchParamsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/PrefetchParamsBoundaryInterface.class", "size": 446, "crc": -2066082407}, {"key": "org/chromium/support_lib_boundary/PrefetchStatusCodeBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/PrefetchStatusCodeBoundaryInterface.class", "size": 446, "crc": -178253990}, {"key": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey.class", "name": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey.class", "size": 844, "crc": -1803596267}, {"key": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants.class", "name": "org/chromium/support_lib_boundary/ProcessGlobalConfigConstants.class", "size": 754, "crc": -604030829}, {"key": "org/chromium/support_lib_boundary/ProfileBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProfileBoundaryInterface.class", "size": 991, "crc": -523900464}, {"key": "org/chromium/support_lib_boundary/ProfileStoreBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProfileStoreBoundaryInterface.class", "size": 442, "crc": 736384224}, {"key": "org/chromium/support_lib_boundary/ProxyControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ProxyControllerBoundaryInterface.class", "size": 493, "crc": 426765439}, {"key": "org/chromium/support_lib_boundary/SafeBrowsingResponseBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/SafeBrowsingResponseBoundaryInterface.class", "size": 264, "crc": -940426253}, {"key": "org/chromium/support_lib_boundary/ScriptHandlerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ScriptHandlerBoundaryInterface.class", "size": 198, "crc": 1521410461}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerClientBoundaryInterface.class", "size": 372, "crc": -1938073090}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerControllerBoundaryInterface.class", "size": 351, "crc": 1410410978}, {"key": "org/chromium/support_lib_boundary/ServiceWorkerWebSettingsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/ServiceWorkerWebSettingsBoundaryInterface.class", "size": 699, "crc": 1373575429}, {"key": "org/chromium/support_lib_boundary/StaticsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/StaticsBoundaryInterface.class", "size": 882, "crc": 1807946790}, {"key": "org/chromium/support_lib_boundary/TracingControllerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/TracingControllerBoundaryInterface.class", "size": 501, "crc": -968205971}, {"key": "org/chromium/support_lib_boundary/VisualStateCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/VisualStateCallbackBoundaryInterface.class", "size": 215, "crc": -977912150}, {"key": "org/chromium/support_lib_boundary/WebMessageBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageBoundaryInterface.class", "size": 502, "crc": 1476672801}, {"key": "org/chromium/support_lib_boundary/WebMessageCallbackBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageCallbackBoundaryInterface.class", "size": 361, "crc": -1667812285}, {"key": "org/chromium/support_lib_boundary/WebMessageListenerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessageListenerBoundaryInterface.class", "size": 407, "crc": -232831470}, {"key": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface$WebMessagePayloadType.class", "name": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface$WebMessagePayloadType.class", "size": 609, "crc": -1633319233}, {"key": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessagePayloadBoundaryInterface.class", "size": 632, "crc": 132272637}, {"key": "org/chromium/support_lib_boundary/WebMessagePortBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebMessagePortBoundaryInterface.class", "size": 367, "crc": -978995146}, {"key": "org/chromium/support_lib_boundary/WebResourceErrorBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebResourceErrorBoundaryInterface.class", "size": 264, "crc": 741762778}, {"key": "org/chromium/support_lib_boundary/WebResourceRequestBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebResourceRequestBoundaryInterface.class", "size": 212, "crc": -72091953}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$AttributionBehavior.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$AttributionBehavior.class", "size": 693, "crc": -794881792}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$ForceDarkBehavior.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$ForceDarkBehavior.class", "size": 644, "crc": 194043176}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$SpeculativeLoadingStatus.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$SpeculativeLoadingStatus.class", "size": 594, "crc": 79341145}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus.class", "size": 742, "crc": 416505625}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebauthnSupport.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface$WebauthnSupport.class", "size": 589, "crc": -817066326}, {"key": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebSettingsBoundaryInterface.class", "size": 2487, "crc": 758370801}, {"key": "org/chromium/support_lib_boundary/WebViewClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewClientBoundaryInterface.class", "size": 819, "crc": -42292662}, {"key": "org/chromium/support_lib_boundary/WebViewCookieManagerBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewCookieManagerBoundaryInterface.class", "size": 331, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebViewProviderBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewProviderBoundaryInterface.class", "size": 1130, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebViewProviderFactoryBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewProviderFactoryBoundaryInterface.class", "size": 591, "crc": **********}, {"key": "org/chromium/support_lib_boundary/WebViewRendererBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewRendererBoundaryInterface.class", "size": 280, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebViewRendererClientBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebViewRendererClientBoundaryInterface.class", "size": 398, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/WebkitToCompatConverterBoundaryInterface.class", "name": "org/chromium/support_lib_boundary/WebkitToCompatConverterBoundaryInterface.class", "size": 730, "crc": -**********}, {"key": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil$InvocationHandlerWithDelegateGetter.class", "name": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil$InvocationHandlerWithDelegateGetter.class", "size": 2442, "crc": 897700894}, {"key": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil.class", "name": "org/chromium/support_lib_boundary/util/BoundaryInterfaceReflectionUtil.class", "size": 5204, "crc": -965536010}, {"key": "org/chromium/support_lib_boundary/util/Features.class", "name": "org/chromium/support_lib_boundary/util/Features.class", "size": 3552, "crc": 922638394}, {"key": "META-INF/androidx.webkit_webkit.version", "name": "META-INF/androidx.webkit_webkit.version", "size": 7, "crc": 2095460740}]