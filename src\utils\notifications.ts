/**
 * Notification System untuk PPWA
 * Mendukung web notifications dan Capacitor local notifications untuk Android
 */

import { LocalNotifications } from '@capacitor/local-notifications';
import { Capacitor } from '@capacitor/core';

export interface NotificationOptions {
  title: string;
  body: string;
  id?: number;
  schedule?: {
    at: Date;
  };
  sound?: string;
  attachments?: Array<{
    id: string;
    url: string;
    options?: any;
  }>;
  actionTypeId?: string;
  extra?: any;
}

class NotificationManager {
  private isCapacitor: boolean;
  private permissionGranted: boolean = false;

  constructor() {
    this.isCapacitor = Capacitor.isNativePlatform();
    this.init();
  }

  /**
   * Initialize notification system
   */
  private async init(): Promise<void> {
    try {
      if (this.isCapacitor) {
        // Request permissions for Capacitor
        const permission = await LocalNotifications.requestPermissions();
        this.permissionGranted = permission.display === 'granted';
        console.log('[Notifications] Capacitor permission:', permission.display);
      } else {
        // Request permissions for web notifications
        if ('Notification' in window) {
          if (Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            this.permissionGranted = permission === 'granted';
          } else {
            this.permissionGranted = Notification.permission === 'granted';
          }
          console.log('[Notifications] Web permission:', Notification.permission);
        }
      }
    } catch (error) {
      console.error('[Notifications] Error initializing:', error);
    }
  }

  /**
   * Show immediate notification
   */
  async showNotification(options: NotificationOptions): Promise<void> {
    if (!this.permissionGranted) {
      console.warn('[Notifications] Permission not granted');
      return;
    }

    try {
      if (this.isCapacitor) {
        // Use Capacitor Local Notifications
        await LocalNotifications.schedule({
          notifications: [
            {
              title: options.title,
              body: options.body,
              id: options.id || Date.now(),
              schedule: { at: new Date(Date.now() + 1000) }, // Show immediately
              sound: options.sound || 'default',
              attachments: options.attachments,
              actionTypeId: options.actionTypeId,
              extra: options.extra
            }
          ]
        });
        console.log('[Notifications] Capacitor notification scheduled');
      } else {
        // Use Web Notifications
        new Notification(options.title, {
          body: options.body,
          icon: '/images/icons/icon-192x192.png',
          badge: '/images/icons/icon-192x192.png',
          tag: options.id?.toString() || 'ppwa-notification',
          requireInteraction: true,
          silent: false
        });
        console.log('[Notifications] Web notification shown');
      }
    } catch (error) {
      console.error('[Notifications] Error showing notification:', error);
    }
  }

  /**
   * Schedule notification for future time
   */
  async scheduleNotification(options: NotificationOptions): Promise<void> {
    if (!this.permissionGranted) {
      console.warn('[Notifications] Permission not granted');
      return;
    }

    if (!options.schedule?.at) {
      console.error('[Notifications] Schedule time is required');
      return;
    }

    try {
      if (this.isCapacitor) {
        // Use Capacitor Local Notifications
        await LocalNotifications.schedule({
          notifications: [
            {
              title: options.title,
              body: options.body,
              id: options.id || Date.now(),
              schedule: { at: options.schedule.at },
              sound: options.sound || 'default',
              attachments: options.attachments,
              actionTypeId: options.actionTypeId,
              extra: options.extra
            }
          ]
        });
        console.log('[Notifications] Capacitor notification scheduled for:', options.schedule.at);
      } else {
        // For web, we'll use setTimeout to show notification at the right time
        const now = new Date().getTime();
        const scheduleTime = options.schedule.at.getTime();
        const delay = scheduleTime - now;

        if (delay > 0) {
          setTimeout(() => {
            this.showNotification(options);
          }, delay);
          console.log('[Notifications] Web notification scheduled for:', options.schedule.at);
        } else {
          // If time has passed, show immediately
          this.showNotification(options);
        }
      }
    } catch (error) {
      console.error('[Notifications] Error scheduling notification:', error);
    }
  }

  /**
   * Cancel scheduled notification
   */
  async cancelNotification(id: number): Promise<void> {
    try {
      if (this.isCapacitor) {
        await LocalNotifications.cancel({
          notifications: [{ id }]
        });
        console.log('[Notifications] Capacitor notification cancelled:', id);
      } else {
        // Web notifications can't be cancelled once scheduled with setTimeout
        // This is a limitation of web notifications
        console.log('[Notifications] Web notification cancellation not supported');
      }
    } catch (error) {
      console.error('[Notifications] Error cancelling notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      if (this.isCapacitor) {
        const pending = await LocalNotifications.getPending();
        if (pending.notifications.length > 0) {
          await LocalNotifications.cancel({
            notifications: pending.notifications.map(n => ({ id: n.id }))
          });
          console.log('[Notifications] All Capacitor notifications cancelled');
        }
      } else {
        console.log('[Notifications] Web notification cancellation not supported');
      }
    } catch (error) {
      console.error('[Notifications] Error cancelling all notifications:', error);
    }
  }

  /**
   * Get pending notifications
   */
  async getPendingNotifications(): Promise<any[]> {
    try {
      if (this.isCapacitor) {
        const pending = await LocalNotifications.getPending();
        return pending.notifications;
      } else {
        // Web notifications don't have a way to get pending notifications
        return [];
      }
    } catch (error) {
      console.error('[Notifications] Error getting pending notifications:', error);
      return [];
    }
  }

  /**
   * Check if notifications are supported
   */
  isSupported(): boolean {
    if (this.isCapacitor) {
      return true; // Capacitor always supports notifications
    } else {
      return 'Notification' in window;
    }
  }

  /**
   * Check if permission is granted
   */
  hasPermission(): boolean {
    return this.permissionGranted;
  }

  /**
   * Request permission (if not already granted)
   */
  async requestPermission(): Promise<boolean> {
    try {
      if (this.isCapacitor) {
        const permission = await LocalNotifications.requestPermissions();
        this.permissionGranted = permission.display === 'granted';
      } else {
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          this.permissionGranted = permission === 'granted';
        }
      }
      return this.permissionGranted;
    } catch (error) {
      console.error('[Notifications] Error requesting permission:', error);
      return false;
    }
  }

  /**
   * Add notification action listeners (for Capacitor)
   */
  addActionListener(callback: (notification: any) => void): void {
    if (this.isCapacitor) {
      LocalNotifications.addListener('localNotificationActionPerformed', callback);
    }
  }

  /**
   * Add notification received listeners (for Capacitor)
   */
  addReceivedListener(callback: (notification: any) => void): void {
    if (this.isCapacitor) {
      LocalNotifications.addListener('localNotificationReceived', callback);
    }
  }
}

// Export singleton instance
export const notificationManager = new NotificationManager();
