[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 1308, "crc": -386623300}, {"key": "com/google/gson/FieldNamingPolicy$5.class", "name": "com/google/gson/FieldNamingPolicy$5.class", "size": 993, "crc": -1140949807}, {"key": "com/google/gson/Gson$5.class", "name": "com/google/gson/Gson$5.class", "size": 2584, "crc": 701101311}, {"key": "com/google/gson/JsonDeserializer.class", "name": "com/google/gson/JsonDeserializer.class", "size": 510, "crc": -1597542562}, {"key": "com/google/gson/JsonStreamParser.class", "name": "com/google/gson/JsonStreamParser.class", "size": 2500, "crc": -454785036}, {"key": "com/google/gson/Gson$FutureTypeAdapter.class", "name": "com/google/gson/Gson$FutureTypeAdapter.class", "size": 2301, "crc": 298578031}, {"key": "com/google/gson/Gson.class", "name": "com/google/gson/Gson.class", "size": 25032, "crc": 2134795115}, {"key": "com/google/gson/FieldNamingStrategy.class", "name": "com/google/gson/FieldNamingStrategy.class", "size": 207, "crc": -1954143911}, {"key": "com/google/gson/FieldNamingPolicy$7.class", "name": "com/google/gson/FieldNamingPolicy$7.class", "size": 993, "crc": -857424614}, {"key": "com/google/gson/Gson$3.class", "name": "com/google/gson/Gson$3.class", "size": 1928, "crc": -179854892}, {"key": "com/google/gson/JsonSerializer.class", "name": "com/google/gson/JsonSerializer.class", "size": 437, "crc": -1952938794}, {"key": "com/google/gson/FieldNamingPolicy$3.class", "name": "com/google/gson/FieldNamingPolicy$3.class", "size": 914, "crc": -200817078}, {"key": "com/google/gson/ReflectionAccessFilter.class", "name": "com/google/gson/ReflectionAccessFilter.class", "size": 1015, "crc": 816757335}, {"key": "com/google/gson/JsonNull.class", "name": "com/google/gson/JsonNull.class", "size": 925, "crc": 51141156}, {"key": "com/google/gson/InstanceCreator.class", "name": "com/google/gson/InstanceCreator.class", "size": 302, "crc": -141635008}, {"key": "com/google/gson/JsonSerializationContext.class", "name": "com/google/gson/JsonSerializationContext.class", "size": 301, "crc": 98276545}, {"key": "com/google/gson/FieldNamingPolicy$1.class", "name": "com/google/gson/FieldNamingPolicy$1.class", "size": 711, "crc": 1967269527}, {"key": "com/google/gson/JsonElement.class", "name": "com/google/gson/JsonElement.class", "size": 3894, "crc": 611966713}, {"key": "com/google/gson/Gson$1.class", "name": "com/google/gson/Gson$1.class", "size": 2003, "crc": -175673792}, {"key": "com/google/gson/FieldNamingPolicy$6.class", "name": "com/google/gson/FieldNamingPolicy$6.class", "size": 993, "crc": 525967747}, {"key": "com/google/gson/TypeAdapter$1.class", "name": "com/google/gson/TypeAdapter$1.class", "size": 1665, "crc": -2079824396}, {"key": "com/google/gson/Gson$4.class", "name": "com/google/gson/Gson$4.class", "size": 1746, "crc": -927984166}, {"key": "com/google/gson/stream/JsonReader$1.class", "name": "com/google/gson/stream/JsonReader$1.class", "size": 1369, "crc": 429140569}, {"key": "com/google/gson/stream/JsonReader.class", "name": "com/google/gson/stream/JsonReader.class", "size": 20194, "crc": -1951910737}, {"key": "com/google/gson/stream/JsonToken.class", "name": "com/google/gson/stream/JsonToken.class", "size": 1465, "crc": 414452971}, {"key": "com/google/gson/stream/MalformedJsonException.class", "name": "com/google/gson/stream/MalformedJsonException.class", "size": 755, "crc": -1956663955}, {"key": "com/google/gson/stream/package-info.class", "name": "com/google/gson/stream/package-info.class", "size": 128, "crc": -1353172601}, {"key": "com/google/gson/stream/JsonWriter.class", "name": "com/google/gson/stream/JsonWriter.class", "size": 9887, "crc": 477303075}, {"key": "com/google/gson/stream/JsonScope.class", "name": "com/google/gson/stream/JsonScope.class", "size": 612, "crc": 169419369}, {"key": "com/google/gson/FieldNamingPolicy$4.class", "name": "com/google/gson/FieldNamingPolicy$4.class", "size": 993, "crc": 612691162}, {"key": "com/google/gson/JsonIOException.class", "name": "com/google/gson/JsonIOException.class", "size": 731, "crc": 1797895160}, {"key": "com/google/gson/reflect/TypeToken.class", "name": "com/google/gson/reflect/TypeToken.class", "size": 9774, "crc": 777970421}, {"key": "com/google/gson/reflect/package-info.class", "name": "com/google/gson/reflect/package-info.class", "size": 129, "crc": 1176388271}, {"key": "com/google/gson/TypeAdapter.class", "name": "com/google/gson/TypeAdapter.class", "size": 3487, "crc": -1514241334}, {"key": "com/google/gson/JsonPrimitive.class", "name": "com/google/gson/JsonPrimitive.class", "size": 5419, "crc": -1673983483}, {"key": "com/google/gson/internal/ConstructorConstructor$19.class", "name": "com/google/gson/internal/ConstructorConstructor$19.class", "size": 1887, "crc": 1021415646}, {"key": "com/google/gson/internal/ConstructorConstructor$1.class", "name": "com/google/gson/internal/ConstructorConstructor$1.class", "size": 1268, "crc": 1981768297}, {"key": "com/google/gson/internal/ReflectionAccessFilterHelper$1.class", "name": "com/google/gson/internal/ReflectionAccessFilterHelper$1.class", "size": 282, "crc": 1631692553}, {"key": "com/google/gson/internal/ConstructorConstructor$3.class", "name": "com/google/gson/internal/ConstructorConstructor$3.class", "size": 1116, "crc": -1638739234}, {"key": "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite.class", "name": "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite.class", "size": 1478, "crc": 1646465888}, {"key": "com/google/gson/internal/ConstructorConstructor$7.class", "name": "com/google/gson/internal/ConstructorConstructor$7.class", "size": 1117, "crc": -332330243}, {"key": "com/google/gson/internal/Excluder$1.class", "name": "com/google/gson/internal/Excluder$1.class", "size": 2346, "crc": 1428224264}, {"key": "com/google/gson/internal/ConstructorConstructor$5.class", "name": "com/google/gson/internal/ConstructorConstructor$5.class", "size": 1686, "crc": -572120621}, {"key": "com/google/gson/internal/$Gson$Types.class", "name": "com/google/gson/internal/$Gson$Types.class", "size": 11930, "crc": -873856899}, {"key": "com/google/gson/internal/Streams$1.class", "name": "com/google/gson/internal/Streams$1.class", "size": 219, "crc": -784988597}, {"key": "com/google/gson/internal/ConstructorConstructor.class", "name": "com/google/gson/internal/ConstructorConstructor.class", "size": 9467, "crc": 1848382014}, {"key": "com/google/gson/internal/ConstructorConstructor$20.class", "name": "com/google/gson/internal/ConstructorConstructor$20.class", "size": 1115, "crc": 1146658508}, {"key": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker.class", "name": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker.class", "size": 1639, "crc": 1186038057}, {"key": "com/google/gson/internal/ConstructorConstructor$2.class", "name": "com/google/gson/internal/ConstructorConstructor$2.class", "size": 1271, "crc": -549336523}, {"key": "com/google/gson/internal/LinkedTreeMap$KeySet.class", "name": "com/google/gson/internal/LinkedTreeMap$KeySet.class", "size": 1678, "crc": 2006679521}, {"key": "com/google/gson/internal/GsonBuildConfig.class", "name": "com/google/gson/internal/GsonBuildConfig.class", "size": 395, "crc": 96162423}, {"key": "com/google/gson/internal/LinkedTreeMap$1.class", "name": "com/google/gson/internal/LinkedTreeMap$1.class", "size": 906, "crc": -721117438}, {"key": "com/google/gson/internal/ConstructorConstructor$18.class", "name": "com/google/gson/internal/ConstructorConstructor$18.class", "size": 896, "crc": 32407761}, {"key": "com/google/gson/internal/LazilyParsedNumber.class", "name": "com/google/gson/internal/LazilyParsedNumber.class", "size": 2222, "crc": -1008866475}, {"key": "com/google/gson/internal/JsonReaderInternalAccess.class", "name": "com/google/gson/internal/JsonReaderInternalAccess.class", "size": 484, "crc": 208026303}, {"key": "com/google/gson/internal/reflect/ReflectionHelper.class", "name": "com/google/gson/internal/reflect/ReflectionHelper.class", "size": 6864, "crc": 988802174}, {"key": "com/google/gson/internal/reflect/ReflectionHelper$1.class", "name": "com/google/gson/internal/reflect/ReflectionHelper$1.class", "size": 262, "crc": 1148836253}, {"key": "com/google/gson/internal/reflect/ReflectionHelper$RecordSupportedHelper.class", "name": "com/google/gson/internal/reflect/ReflectionHelper$RecordSupportedHelper.class", "size": 3636, "crc": 1690362535}, {"key": "com/google/gson/internal/reflect/ReflectionHelper$RecordNotSupportedHelper.class", "name": "com/google/gson/internal/reflect/ReflectionHelper$RecordNotSupportedHelper.class", "size": 2063, "crc": 552100455}, {"key": "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper.class", "name": "com/google/gson/internal/reflect/ReflectionHelper$RecordHelper.class", "size": 1307, "crc": -842099516}, {"key": "com/google/gson/internal/LinkedTreeMap$EntrySet.class", "name": "com/google/gson/internal/LinkedTreeMap$EntrySet.class", "size": 2055, "crc": 1837191245}, {"key": "com/google/gson/internal/ConstructorConstructor$4.class", "name": "com/google/gson/internal/ConstructorConstructor$4.class", "size": 1107, "crc": -240888643}, {"key": "com/google/gson/internal/ObjectConstructor.class", "name": "com/google/gson/internal/ObjectConstructor.class", "size": 262, "crc": **********}, {"key": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator.class", "name": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator.class", "size": 2021, "crc": 833466761}, {"key": "com/google/gson/internal/PreJava9DateFormatProvider.class", "name": "com/google/gson/internal/PreJava9DateFormatProvider.class", "size": 1943, "crc": **********}, {"key": "com/google/gson/internal/$Gson$Preconditions.class", "name": "com/google/gson/internal/$Gson$Preconditions.class", "size": 976, "crc": 13028340}, {"key": "com/google/gson/internal/ConstructorConstructor$6.class", "name": "com/google/gson/internal/ConstructorConstructor$6.class", "size": 1663, "crc": -**********}, {"key": "com/google/gson/internal/Streams.class", "name": "com/google/gson/internal/Streams.class", "size": 2432, "crc": 778143066}, {"key": "com/google/gson/internal/ReflectionAccessFilterHelper.class", "name": "com/google/gson/internal/ReflectionAccessFilterHelper.class", "size": 3007, "crc": **********}, {"key": "com/google/gson/internal/ConstructorConstructor$12.class", "name": "com/google/gson/internal/ConstructorConstructor$12.class", "size": 878, "crc": -**********}, {"key": "com/google/gson/internal/UnsafeAllocator.class", "name": "com/google/gson/internal/UnsafeAllocator.class", "size": 3202, "crc": -1080030532}, {"key": "com/google/gson/internal/ConstructorConstructor$10.class", "name": "com/google/gson/internal/ConstructorConstructor$10.class", "size": 875, "crc": -1742765116}, {"key": "com/google/gson/internal/package-info.class", "name": "com/google/gson/internal/package-info.class", "size": 130, "crc": -918850782}, {"key": "com/google/gson/internal/UnsafeAllocator$4.class", "name": "com/google/gson/internal/UnsafeAllocator$4.class", "size": 1210, "crc": -1412657780}, {"key": "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl.class", "size": 3285, "crc": 1107402572}, {"key": "com/google/gson/internal/ConstructorConstructor$8.class", "name": "com/google/gson/internal/ConstructorConstructor$8.class", "size": 1126, "crc": -479786240}, {"key": "com/google/gson/internal/ConstructorConstructor$14.class", "name": "com/google/gson/internal/ConstructorConstructor$14.class", "size": 900, "crc": 714512750}, {"key": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$1.class", "name": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$1.class", "size": 1498, "crc": -1193881547}, {"key": "com/google/gson/internal/LinkedTreeMap.class", "name": "com/google/gson/internal/LinkedTreeMap.class", "size": 11779, "crc": 2096948832}, {"key": "com/google/gson/internal/UnsafeAllocator$2.class", "name": "com/google/gson/internal/UnsafeAllocator$2.class", "size": 1230, "crc": -2008576229}, {"key": "com/google/gson/internal/ConstructorConstructor$16.class", "name": "com/google/gson/internal/ConstructorConstructor$16.class", "size": 875, "crc": 1637154713}, {"key": "com/google/gson/internal/Primitives.class", "name": "com/google/gson/internal/Primitives.class", "size": 1584, "crc": 1080132466}, {"key": "com/google/gson/internal/LinkedTreeMap$KeySet$1.class", "name": "com/google/gson/internal/LinkedTreeMap$KeySet$1.class", "size": 1312, "crc": 2144335107}, {"key": "com/google/gson/internal/ConstructorConstructor$11.class", "name": "com/google/gson/internal/ConstructorConstructor$11.class", "size": 881, "crc": -331927281}, {"key": "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl.class", "size": 1636, "crc": -267158792}, {"key": "com/google/gson/internal/ConstructorConstructor$9.class", "name": "com/google/gson/internal/ConstructorConstructor$9.class", "size": 2342, "crc": -822932889}, {"key": "com/google/gson/internal/$Gson$Types$WildcardTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$WildcardTypeImpl.class", "size": 2372, "crc": -427480978}, {"key": "com/google/gson/internal/ConstructorConstructor$13.class", "name": "com/google/gson/internal/ConstructorConstructor$13.class", "size": 877, "crc": -1909473327}, {"key": "com/google/gson/internal/bind/DefaultDateTypeAdapter$1.class", "name": "com/google/gson/internal/bind/DefaultDateTypeAdapter$1.class", "size": 274, "crc": -1274430215}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter.class", "size": 6159, "crc": 718005824}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$RecordAdapter.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$RecordAdapter.class", "size": 7002, "crc": 776015891}, {"key": "com/google/gson/internal/bind/TypeAdapters$8.class", "name": "com/google/gson/internal/bind/TypeAdapters$8.class", "size": 1829, "crc": 410976209}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter.class", "size": 5241, "crc": -1131980587}, {"key": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.class", "size": 4190, "crc": -1876784797}, {"key": "com/google/gson/internal/bind/TypeAdapters$13.class", "name": "com/google/gson/internal/bind/TypeAdapters$13.class", "size": 1856, "crc": 2047384755}, {"key": "com/google/gson/internal/bind/TypeAdapters$11.class", "name": "com/google/gson/internal/bind/TypeAdapters$11.class", "size": 2043, "crc": 1191761240}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter$1.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter$1.class", "size": 1457, "crc": 2111702006}, {"key": "com/google/gson/internal/bind/JsonTreeReader$2.class", "name": "com/google/gson/internal/bind/JsonTreeReader$2.class", "size": 853, "crc": -2068473893}, {"key": "com/google/gson/internal/bind/TypeAdapters$35.class", "name": "com/google/gson/internal/bind/TypeAdapters$35.class", "size": 942, "crc": -1384861465}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1.class", "size": 5053, "crc": 1541718142}, {"key": "com/google/gson/internal/bind/JsonTreeReader.class", "name": "com/google/gson/internal/bind/JsonTreeReader.class", "size": 8918, "crc": 1731942151}, {"key": "com/google/gson/internal/bind/TypeAdapters$31.class", "name": "com/google/gson/internal/bind/TypeAdapters$31.class", "size": 1797, "crc": 1432688262}, {"key": "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.class", "name": "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.class", "size": 3167, "crc": -187010973}, {"key": "com/google/gson/internal/bind/NumberTypeAdapter$1.class", "name": "com/google/gson/internal/bind/NumberTypeAdapter$1.class", "size": 1399, "crc": -1497915762}, {"key": "com/google/gson/internal/bind/DateTypeAdapter.class", "name": "com/google/gson/internal/bind/DateTypeAdapter.class", "size": 4165, "crc": 867961230}, {"key": "com/google/gson/internal/bind/TypeAdapters$28.class", "name": "com/google/gson/internal/bind/TypeAdapters$28.class", "size": 5914, "crc": -814105585}, {"key": "com/google/gson/internal/bind/TypeAdapters$15.class", "name": "com/google/gson/internal/bind/TypeAdapters$15.class", "size": 1865, "crc": -2090417274}, {"key": "com/google/gson/internal/bind/TypeAdapters$17.class", "name": "com/google/gson/internal/bind/TypeAdapters$17.class", "size": 2288, "crc": -28642768}, {"key": "com/google/gson/internal/bind/NumberTypeAdapter.class", "name": "com/google/gson/internal/bind/NumberTypeAdapter.class", "size": 3054, "crc": 1204810526}, {"key": "com/google/gson/internal/bind/util/ISO8601Utils.class", "name": "com/google/gson/internal/bind/util/ISO8601Utils.class", "size": 7285, "crc": -1119145243}, {"key": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter.class", "name": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter.class", "size": 4175, "crc": 1095616872}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter.class", "size": 4005, "crc": 846178098}, {"key": "com/google/gson/internal/bind/TypeAdapters$33.class", "name": "com/google/gson/internal/bind/TypeAdapters$33.class", "size": 1986, "crc": 602527738}, {"key": "com/google/gson/internal/bind/TypeAdapters$34$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$34$1.class", "size": 2208, "crc": -1077645602}, {"key": "com/google/gson/internal/bind/TypeAdapters$10.class", "name": "com/google/gson/internal/bind/TypeAdapters$10.class", "size": 2686, "crc": -136075567}, {"key": "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter.class", "name": "com/google/gson/internal/bind/SerializationDelegatingTypeAdapter.class", "size": 710, "crc": -1177008025}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl.class", "size": 2087, "crc": 759937658}, {"key": "com/google/gson/internal/bind/TypeAdapters$34.class", "name": "com/google/gson/internal/bind/TypeAdapters$34.class", "size": 2068, "crc": -1687132468}, {"key": "com/google/gson/internal/bind/JsonTreeReader$1.class", "name": "com/google/gson/internal/bind/JsonTreeReader$1.class", "size": 691, "crc": 1056655485}, {"key": "com/google/gson/internal/bind/TypeAdapters$9.class", "name": "com/google/gson/internal/bind/TypeAdapters$9.class", "size": 1613, "crc": -1546499447}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory.class", "size": 2653, "crc": -1911119271}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField.class", "size": 1308, "crc": 1684991270}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter$2.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter$2.class", "size": 956, "crc": -1423937598}, {"key": "com/google/gson/internal/bind/TypeAdapters$12.class", "name": "com/google/gson/internal/bind/TypeAdapters$12.class", "size": 1926, "crc": -1596227597}, {"key": "com/google/gson/internal/bind/TypeAdapters$16.class", "name": "com/google/gson/internal/bind/TypeAdapters$16.class", "size": 2288, "crc": -844915683}, {"key": "com/google/gson/internal/bind/DateTypeAdapter$1.class", "name": "com/google/gson/internal/bind/DateTypeAdapter$1.class", "size": 1165, "crc": -1540749527}, {"key": "com/google/gson/internal/bind/TypeAdapters$32.class", "name": "com/google/gson/internal/bind/TypeAdapters$32.class", "size": 1975, "crc": -580064636}, {"key": "com/google/gson/internal/bind/NumberTypeAdapter$2.class", "name": "com/google/gson/internal/bind/NumberTypeAdapter$2.class", "size": 802, "crc": -1375602292}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$1.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$1.class", "size": 253, "crc": 1610195683}, {"key": "com/google/gson/internal/bind/TypeAdapters$30.class", "name": "com/google/gson/internal/bind/TypeAdapters$30.class", "size": 1427, "crc": 1935487221}, {"key": "com/google/gson/internal/bind/TypeAdapters$14.class", "name": "com/google/gson/internal/bind/TypeAdapters$14.class", "size": 2312, "crc": 360068070}, {"key": "com/google/gson/internal/bind/TypeAdapters$29.class", "name": "com/google/gson/internal/bind/TypeAdapters$29.class", "size": 1592, "crc": 516662804}, {"key": "com/google/gson/internal/bind/TypeAdapters$25.class", "name": "com/google/gson/internal/bind/TypeAdapters$25.class", "size": 2144, "crc": -1702744295}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldReflectionAdapter.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldReflectionAdapter.class", "size": 2646, "crc": -94273670}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.class", "size": 12357, "crc": -1985942822}, {"key": "com/google/gson/internal/bind/TypeAdapters$18.class", "name": "com/google/gson/internal/bind/TypeAdapters$18.class", "size": 1878, "crc": -557548977}, {"key": "com/google/gson/internal/bind/TypeAdapters$3.class", "name": "com/google/gson/internal/bind/TypeAdapters$3.class", "size": 1900, "crc": 1396595257}, {"key": "com/google/gson/internal/bind/TypeAdapters$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$1.class", "size": 1730, "crc": -711923786}, {"key": "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.class", "size": 7495, "crc": 1409739951}, {"key": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType$1.class", "name": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType$1.class", "size": 944, "crc": 396003131}, {"key": "com/google/gson/internal/bind/TypeAdapters$27.class", "name": "com/google/gson/internal/bind/TypeAdapters$27.class", "size": 2400, "crc": 627985178}, {"key": "com/google/gson/internal/bind/TypeAdapters$23.class", "name": "com/google/gson/internal/bind/TypeAdapters$23.class", "size": 1886, "crc": -1921142174}, {"key": "com/google/gson/internal/bind/TypeAdapters$5.class", "name": "com/google/gson/internal/bind/TypeAdapters$5.class", "size": 2462, "crc": 1597900717}, {"key": "com/google/gson/internal/bind/DefaultDateTypeAdapter.class", "name": "com/google/gson/internal/bind/DefaultDateTypeAdapter.class", "size": 7412, "crc": -1919098834}, {"key": "com/google/gson/internal/bind/ArrayTypeAdapter$1.class", "name": "com/google/gson/internal/bind/ArrayTypeAdapter$1.class", "size": 2039, "crc": -97166711}, {"key": "com/google/gson/internal/bind/TypeAdapters$7.class", "name": "com/google/gson/internal/bind/TypeAdapters$7.class", "size": 2046, "crc": 1715600818}, {"key": "com/google/gson/internal/bind/CollectionTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/CollectionTypeAdapterFactory.class", "size": 2660, "crc": -730090529}, {"key": "com/google/gson/internal/bind/TypeAdapters$21.class", "name": "com/google/gson/internal/bind/TypeAdapters$21.class", "size": 1930, "crc": 691824730}, {"key": "com/google/gson/internal/bind/MapTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/MapTypeAdapterFactory.class", "size": 3385, "crc": 1287885040}, {"key": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1.class", "size": 1998, "crc": 1867720281}, {"key": "com/google/gson/internal/bind/TypeAdapters.class", "name": "com/google/gson/internal/bind/TypeAdapters.class", "size": 9841, "crc": -1172175011}, {"key": "com/google/gson/internal/bind/TypeAdapters$26.class", "name": "com/google/gson/internal/bind/TypeAdapters$26.class", "size": 2930, "crc": -1817461362}, {"key": "com/google/gson/internal/bind/JsonTreeWriter.class", "name": "com/google/gson/internal/bind/JsonTreeWriter.class", "size": 5985, "crc": 74853638}, {"key": "com/google/gson/internal/bind/TypeAdapters$19.class", "name": "com/google/gson/internal/bind/TypeAdapters$19.class", "size": 1866, "crc": -190557122}, {"key": "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.class", "size": 3657, "crc": -1078808592}, {"key": "com/google/gson/internal/bind/TypeAdapters$24.class", "name": "com/google/gson/internal/bind/TypeAdapters$24.class", "size": 2332, "crc": 1463170420}, {"key": "com/google/gson/internal/bind/JsonTreeWriter$1.class", "name": "com/google/gson/internal/bind/JsonTreeWriter$1.class", "size": 766, "crc": -1011114111}, {"key": "com/google/gson/internal/bind/TypeAdapters$2.class", "name": "com/google/gson/internal/bind/TypeAdapters$2.class", "size": 3002, "crc": 1683006161}, {"key": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType.class", "name": "com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType.class", "size": 3043, "crc": -267067282}, {"key": "com/google/gson/internal/bind/TypeAdapters$6.class", "name": "com/google/gson/internal/bind/TypeAdapters$6.class", "size": 2471, "crc": -1890332639}, {"key": "com/google/gson/internal/bind/TypeAdapters$20.class", "name": "com/google/gson/internal/bind/TypeAdapters$20.class", "size": 1861, "crc": -488959519}, {"key": "com/google/gson/internal/bind/TypeAdapters$22.class", "name": "com/google/gson/internal/bind/TypeAdapters$22.class", "size": 2124, "crc": 24458268}, {"key": "com/google/gson/internal/bind/TypeAdapters$4.class", "name": "com/google/gson/internal/bind/TypeAdapters$4.class", "size": 1869, "crc": 635733714}, {"key": "com/google/gson/internal/bind/ArrayTypeAdapter.class", "name": "com/google/gson/internal/bind/ArrayTypeAdapter.class", "size": 3440, "crc": -263907547}, {"key": "com/google/gson/internal/ConstructorConstructor$17.class", "name": "com/google/gson/internal/ConstructorConstructor$17.class", "size": 881, "crc": 870983117}, {"key": "com/google/gson/internal/UnsafeAllocator$3.class", "name": "com/google/gson/internal/UnsafeAllocator$3.class", "size": 1109, "crc": 389582085}, {"key": "com/google/gson/internal/LinkedTreeMap$EntrySet$1.class", "name": "com/google/gson/internal/LinkedTreeMap$EntrySet$1.class", "size": 1511, "crc": 1649642875}, {"key": "com/google/gson/internal/Excluder.class", "name": "com/google/gson/internal/Excluder.class", "size": 7221, "crc": -568958265}, {"key": "com/google/gson/internal/NonNullElementWrapperList.class", "name": "com/google/gson/internal/NonNullElementWrapperList.class", "size": 3540, "crc": 290938734}, {"key": "com/google/gson/internal/Streams$AppendableWriter.class", "name": "com/google/gson/internal/Streams$AppendableWriter.class", "size": 2306, "crc": -1813001609}, {"key": "com/google/gson/internal/UnsafeAllocator$1.class", "name": "com/google/gson/internal/UnsafeAllocator$1.class", "size": 1187, "crc": -1646040788}, {"key": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$2.class", "name": "com/google/gson/internal/ReflectionAccessFilterHelper$AccessChecker$2.class", "size": 955, "crc": 406296730}, {"key": "com/google/gson/internal/JavaVersion.class", "name": "com/google/gson/internal/JavaVersion.class", "size": 1990, "crc": 571631101}, {"key": "com/google/gson/internal/LinkedTreeMap$Node.class", "name": "com/google/gson/internal/LinkedTreeMap$Node.class", "size": 3601, "crc": -227775785}, {"key": "com/google/gson/internal/ConstructorConstructor$15.class", "name": "com/google/gson/internal/ConstructorConstructor$15.class", "size": 896, "crc": 744008631}, {"key": "com/google/gson/internal/sql/SqlTypesSupport$1.class", "name": "com/google/gson/internal/sql/SqlTypesSupport$1.class", "size": 1151, "crc": 2069998286}, {"key": "com/google/gson/internal/sql/SqlTimestampTypeAdapter$1.class", "name": "com/google/gson/internal/sql/SqlTimestampTypeAdapter$1.class", "size": 1516, "crc": 2127776425}, {"key": "com/google/gson/internal/sql/SqlTimestampTypeAdapter.class", "name": "com/google/gson/internal/sql/SqlTimestampTypeAdapter.class", "size": 2166, "crc": -1436323083}, {"key": "com/google/gson/internal/sql/SqlDateTypeAdapter$1.class", "name": "com/google/gson/internal/sql/SqlDateTypeAdapter$1.class", "size": 1244, "crc": 406723721}, {"key": "com/google/gson/internal/sql/SqlTypesSupport$2.class", "name": "com/google/gson/internal/sql/SqlTypesSupport$2.class", "size": 1171, "crc": -2048106006}, {"key": "com/google/gson/internal/sql/SqlTimeTypeAdapter$1.class", "name": "com/google/gson/internal/sql/SqlTimeTypeAdapter$1.class", "size": 1236, "crc": -47427234}, {"key": "com/google/gson/internal/sql/SqlTypesSupport.class", "name": "com/google/gson/internal/sql/SqlTypesSupport.class", "size": 1841, "crc": -86153831}, {"key": "com/google/gson/internal/sql/SqlDateTypeAdapter.class", "name": "com/google/gson/internal/sql/SqlDateTypeAdapter.class", "size": 3289, "crc": -298135764}, {"key": "com/google/gson/internal/sql/SqlTimeTypeAdapter.class", "name": "com/google/gson/internal/sql/SqlTimeTypeAdapter.class", "size": 3258, "crc": 703128326}, {"key": "com/google/gson/FieldNamingPolicy$2.class", "name": "com/google/gson/FieldNamingPolicy$2.class", "size": 837, "crc": -789273759}, {"key": "com/google/gson/Gson$2.class", "name": "com/google/gson/Gson$2.class", "size": 2077, "crc": -925335759}, {"key": "com/google/gson/ToNumberPolicy.class", "name": "com/google/gson/ToNumberPolicy.class", "size": 1646, "crc": -167565090}, {"key": "com/google/gson/annotations/SerializedName.class", "name": "com/google/gson/annotations/SerializedName.class", "size": 566, "crc": 855047197}, {"key": "com/google/gson/annotations/Expose.class", "name": "com/google/gson/annotations/Expose.class", "size": 523, "crc": -1615081736}, {"key": "com/google/gson/annotations/JsonAdapter.class", "name": "com/google/gson/annotations/JsonAdapter.class", "size": 548, "crc": 703917070}, {"key": "com/google/gson/annotations/package-info.class", "name": "com/google/gson/annotations/package-info.class", "size": 133, "crc": 1197744022}, {"key": "com/google/gson/annotations/Until.class", "name": "com/google/gson/annotations/Until.class", "size": 456, "crc": -833630988}, {"key": "com/google/gson/annotations/Since.class", "name": "com/google/gson/annotations/Since.class", "size": 456, "crc": -940047775}, {"key": "com/google/gson/TypeAdapterFactory.class", "name": "com/google/gson/TypeAdapterFactory.class", "size": 384, "crc": 1537392089}, {"key": "com/google/gson/ToNumberPolicy$4.class", "name": "com/google/gson/ToNumberPolicy$4.class", "size": 1577, "crc": 543563887}, {"key": "com/google/gson/package-info.class", "name": "com/google/gson/package-info.class", "size": 121, "crc": -852115501}, {"key": "com/google/gson/LongSerializationPolicy.class", "name": "com/google/gson/LongSerializationPolicy.class", "size": 1507, "crc": 1147379560}, {"key": "com/google/gson/ReflectionAccessFilter$1.class", "name": "com/google/gson/ReflectionAccessFilter$1.class", "size": 1129, "crc": 1973610457}, {"key": "com/google/gson/FieldNamingPolicy.class", "name": "com/google/gson/FieldNamingPolicy.class", "size": 3292, "crc": -1046404405}, {"key": "com/google/gson/LongSerializationPolicy$1.class", "name": "com/google/gson/LongSerializationPolicy$1.class", "size": 856, "crc": *********}, {"key": "com/google/gson/ToNumberPolicy$2.class", "name": "com/google/gson/ToNumberPolicy$2.class", "size": 911, "crc": -*********}, {"key": "com/google/gson/ReflectionAccessFilter$3.class", "name": "com/google/gson/ReflectionAccessFilter$3.class", "size": 1123, "crc": 1541292602}, {"key": "com/google/gson/JsonSyntaxException.class", "name": "com/google/gson/JsonSyntaxException.class", "size": 743, "crc": *********}, {"key": "com/google/gson/JsonArray.class", "name": "com/google/gson/JsonArray.class", "size": 5862, "crc": *********}, {"key": "com/google/gson/ReflectionAccessFilter$FilterResult.class", "name": "com/google/gson/ReflectionAccessFilter$FilterResult.class", "size": 1352, "crc": -1829501517}, {"key": "com/google/gson/ReflectionAccessFilter$4.class", "name": "com/google/gson/ReflectionAccessFilter$4.class", "size": 1127, "crc": *********}, {"key": "com/google/gson/ToNumberStrategy.class", "name": "com/google/gson/ToNumberStrategy.class", "size": 256, "crc": 21916265}, {"key": "com/google/gson/JsonParseException.class", "name": "com/google/gson/JsonParseException.class", "size": 732, "crc": 1073984136}, {"key": "com/google/gson/ToNumberPolicy$3.class", "name": "com/google/gson/ToNumberPolicy$3.class", "size": 2000, "crc": -*********}, {"key": "com/google/gson/LongSerializationPolicy$2.class", "name": "com/google/gson/LongSerializationPolicy$2.class", "size": 978, "crc": 2086037239}, {"key": "com/google/gson/JsonParser.class", "name": "com/google/gson/JsonParser.class", "size": 3134, "crc": -598694373}, {"key": "com/google/gson/ReflectionAccessFilter$2.class", "name": "com/google/gson/ReflectionAccessFilter$2.class", "size": 1120, "crc": 1474709424}, {"key": "com/google/gson/GsonBuilder.class", "name": "com/google/gson/GsonBuilder.class", "size": 12064, "crc": 1097821208}, {"key": "com/google/gson/FieldAttributes.class", "name": "com/google/gson/FieldAttributes.class", "size": 1964, "crc": -2025161918}, {"key": "com/google/gson/JsonDeserializationContext.class", "name": "com/google/gson/JsonDeserializationContext.class", "size": 413, "crc": -1332919622}, {"key": "com/google/gson/JsonObject.class", "name": "com/google/gson/JsonObject.class", "size": 4825, "crc": 462228137}, {"key": "com/google/gson/ToNumberPolicy$1.class", "name": "com/google/gson/ToNumberPolicy$1.class", "size": 967, "crc": 932911493}, {"key": "com/google/gson/ExclusionStrategy.class", "name": "com/google/gson/ExclusionStrategy.class", "size": 291, "crc": 1421760418}, {"key": "META-INF/maven/com.google.code.gson/gson/pom.xml", "name": "META-INF/maven/com.google.code.gson/gson/pom.xml", "size": 9365, "crc": 1776036901}, {"key": "META-INF/maven/com.google.code.gson/gson/pom.properties", "name": "META-INF/maven/com.google.code.gson/gson/pom.properties", "size": 60, "crc": 1012330174}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 305, "crc": -868626693}]