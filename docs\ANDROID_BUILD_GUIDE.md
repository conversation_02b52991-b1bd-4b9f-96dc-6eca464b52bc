# 📱 Panduan Build Android APK - PPWA

Panduan lengkap untuk membangun APK Android dari aplikasi PPWA menggunakan Android Studio.

## 📋 Prerequisites

### Software Requirements
- **Node.js** (v20 atau lebih baru)
- **npm** atau **yarn**
- **Android Studio** (versi terbaru)
- **Java Development Kit (JDK)** 17 atau 21
- **Android SDK** (API level 23-35)

### Hardware Requirements
- **RAM**: Minimum 8GB, disarankan 16GB
- **Storage**: Minimum 10GB free space
- **OS**: Windows 10/11, macOS 10.15+, atau Linux Ubuntu 18.04+

## 🔧 Setup Environment

### 1. Install Android Studio
1. Download Android Studio dari [developer.android.com](https://developer.android.com/studio)
2. Install dengan semua komponen default
3. Buka Android Studio dan ikuti setup wizard
4. Install Android SDK dan build tools terbaru

### 2. Setup Environment Variables
Tambahkan ke system PATH atau `.bashrc`/`.zshrc`:

```bash
# Windows (PowerShell)
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools"

# macOS/Linux
export ANDROID_HOME=$HOME/Library/Android/sdk  # macOS
export ANDROID_HOME=$HOME/Android/Sdk          # Linux
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
```

### 3. Verify Installation
```bash
# Check Android SDK
adb --version

# Check Java
java --version

# Check Node.js
node --version
npm --version
```

## 🚀 Build Process

### Step 1: Prepare Project
```bash
# Clone atau navigate ke project directory
cd ppwa

# Install dependencies
npm install

# Build web assets
npm run build
```

### Step 2: Sync Capacitor
```bash
# Sync Capacitor dengan Android platform
npm run cap:sync

# Atau manual
npx cap sync android
```

### Step 3: Open Android Studio
```bash
# Buka project di Android Studio
npm run cap:android

# Atau manual
npx cap open android
```

### Step 4: Configure Android Project

#### A. Update App Information
Edit `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        applicationId "com.ppwa.app"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 1
        versionName "1.0.0"
    }
}
```

#### B. Configure Signing (untuk Release)
1. Generate keystore:
```bash
keytool -genkey -v -keystore ppwa-release-key.keystore -alias ppwa-key -keyalg RSA -keysize 2048 -validity 10000
```

2. Edit `android/app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('ppwa-release-key.keystore')
            storePassword 'your-store-password'
            keyAlias 'ppwa-key'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### Step 5: Build APK

#### Debug Build (untuk testing)
```bash
# Via Android Studio
# 1. Pilih "Build" > "Build Bundle(s) / APK(s)" > "Build APK(s)"
# 2. Tunggu proses build selesai
# 3. APK akan tersedia di: android/app/build/outputs/apk/debug/

# Via Command Line
cd android
./gradlew assembleDebug
```

#### Release Build (untuk production)
```bash
# Via Android Studio
# 1. Pilih "Build" > "Generate Signed Bundle / APK"
# 2. Pilih "APK"
# 3. Pilih keystore dan masukkan password
# 4. Pilih "release" build variant
# 5. Klik "Finish"

# Via Command Line
cd android
./gradlew assembleRelease
```

## 📁 Output Locations

### Debug APK
```
android/app/build/outputs/apk/debug/app-debug.apk
```

### Release APK
```
android/app/build/outputs/apk/release/app-release.apk
```

### AAB (Android App Bundle)
```
android/app/build/outputs/bundle/release/app-release.aab
```

## 🧪 Testing APK

### Install ke Device
```bash
# Install debug APK
adb install android/app/build/outputs/apk/debug/app-debug.apk

# Install release APK
adb install android/app/build/outputs/apk/release/app-release.apk

# Uninstall jika sudah ada
adb uninstall com.ppwa.app
```

### Test Features
1. **Offline Functionality**
   - Matikan internet
   - Buka aplikasi
   - Pastikan carousel dan navigasi berfungsi

2. **Location Services**
   - Buka tab Profile
   - Izinkan akses lokasi
   - Verifikasi koordinat ditampilkan

3. **Device Information**
   - Periksa Device UUID
   - Verifikasi informasi device
   - Test connectivity status

4. **PWA Features**
   - Test "Add to Home Screen"
   - Verifikasi splash screen
   - Test offline indicators

## 🔍 Troubleshooting

### Common Issues

#### 1. Build Failed - SDK Not Found
```bash
# Solution: Set ANDROID_HOME
export ANDROID_HOME=/path/to/android/sdk
```

#### 2. Gradle Build Failed
```bash
# Clean dan rebuild
cd android
./gradlew clean
./gradlew build
```

#### 3. Capacitor Sync Issues
```bash
# Remove dan re-add platform
npx cap remove android
npx cap add android
npx cap sync android
```

#### 4. Permission Denied Errors
```bash
# Fix gradlew permissions (Linux/macOS)
chmod +x android/gradlew
```

#### 5. Out of Memory Error
Edit `android/gradle.properties`:
```properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
```

### Debug Tools

#### Check Device Connection
```bash
adb devices
```

#### View Logs
```bash
# Android logs
adb logcat

# Filter untuk app
adb logcat | grep "com.ppwa.app"
```

#### Chrome DevTools
1. Buka Chrome
2. Go to `chrome://inspect`
3. Pilih device dan app
4. Debug seperti web app

## 📦 Distribution

### Google Play Store
1. Build AAB (Android App Bundle)
2. Upload ke Google Play Console
3. Fill app information
4. Submit for review

### Direct Distribution
1. Build signed APK
2. Enable "Unknown Sources" di device
3. Install APK langsung

## 🔐 Security Considerations

### Code Obfuscation
Edit `android/app/build.gradle`:
```gradle
buildTypes {
    release {
        minifyEnabled true
        shrinkResources true
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
}
```

### Network Security
Edit `android/app/src/main/res/xml/network_security_config.xml`:
```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">your-api-domain.com</domain>
    </domain-config>
</network-security-config>
```

## 📊 Performance Optimization

### Reduce APK Size
1. Enable ProGuard/R8
2. Remove unused resources
3. Use vector drawables
4. Compress images

### Improve Startup Time
1. Optimize splash screen
2. Lazy load components
3. Minimize initial bundle size

## 🎯 Next Steps

1. **Setup CI/CD** untuk automated builds
2. **Configure Firebase** untuk analytics dan crash reporting
3. **Implement App Signing** untuk automatic updates
4. **Setup Beta Testing** dengan Google Play Console

---

**📞 Support**: Jika mengalami masalah, periksa [troubleshooting guide](./TROUBLESHOOTING.md) atau buat issue di repository.
