# PPWA - Progressive Web App dengan React + Capacitor

Aplikasi Progressive Web App (PWA) yang dibangun menggunakan React, TypeScript, Tailwind CSS, DaisyUI, dan Capacitor untuk deployment Android dengan fitur offline-first architecture dan device information.

## 🚀 Fitur Utama

### Core Features
- **Homepage Carousel**: Slider produk interaktif dengan animasi smooth
- **Navigasi Responsif**: 6 tab navigasi (HOME, KALENDER, PROFILE, INFORMATION, PRODUK, LAYANAN)
- **Responsive Design**: Optimasi untuk tablet 11 inch landscape dan mobile
- **Android Ready**: Siap untuk deployment ke Android menggunakan Capacitor
- **Modern UI**: Menggunakan DaisyUI components dengan Tailwind CSS

### Advanced Offline Features
- **Service Worker**: Implementasi offline-first architecture dengan caching strategies
- **IndexedDB Storage**: Local data storage untuk user preferences dan application state
- **Background Sync**: Automatic data synchronization setiap 10 menit
- **Connectivity Management**: Visual indicators untuk status online/offline
- **Data Persistence**: Homepage carousel dan navigasi berfungsi sepenuhnya offline

### Device Information & Location
- **Geolocation**: Menampilkan koordinat device dengan format user-friendly
- **Device UUID**: Unique device identifier untuk tracking
- **Device Info**: Platform, model, OS version, manufacturer details
- **Permission Handling**: Proper request dan error handling untuk location services
- **Real-time Updates**: Refresh location dan device information

### PWA Features
- **Add to Home Screen**: Manifest configuration untuk instalasi PWA
- **App Icons**: Multiple sizes untuk berbagai device
- **Splash Screen**: Custom splash screen untuk Android
- **Offline Indicators**: Toast notifications untuk status connectivity

## 🛠️ Teknologi yang Digunakan

### Frontend Stack
- **React 19** - Library frontend modern
- **TypeScript** - Type safety dan developer experience yang lebih baik
- **Vite** - Build tool yang cepat dan modern
- **Tailwind CSS v4** - Utility-first CSS framework
- **DaisyUI** - Component library untuk Tailwind CSS

### Mobile & PWA
- **Capacitor 7** - Framework untuk deployment ke mobile platform
- **@capacitor/geolocation** - Plugin untuk akses lokasi device
- **@capacitor/device** - Plugin untuk informasi device
- **@capacitor/app** - Plugin untuk app lifecycle management

### Offline & Storage
- **Service Worker** - Caching dan offline functionality
- **IndexedDB** - Local database untuk data persistence
- **Background Sync** - Automatic data synchronization

### Styling & Fonts
- **Poppins Font** - Typography yang clean dan modern
- **Custom CSS** - Animasi dan responsive design

## 📱 Responsive Design

Aplikasi ini dioptimalkan untuk berbagai ukuran layar:

- **Desktop**: 1366px+ (11 inch tablet landscape)
- **Tablet Landscape**: 1024px - 1366px
- **Tablet Portrait**: 768px - 1023px
- **Mobile**: < 768px

## 🏗️ Struktur Proyek

```
src/
├── components/
│   ├── Homepage.tsx      # Komponen homepage dengan carousel
│   ├── Homepage.css      # Styling untuk homepage
│   └── Navigation.tsx    # Komponen navigasi bawah
├── App.tsx              # Komponen utama aplikasi
├── App.css              # Styling global aplikasi
├── index.css            # Konfigurasi Tailwind dan font
└── main.tsx             # Entry point aplikasi
```

## 🚀 Cara Menjalankan

### Development

```bash
# Install dependencies
npm install

# Jalankan development server
npm run dev
```

### Build untuk Production

```bash
# Build aplikasi
npm run build

# Preview build
npm run preview
```

### Android Development

```bash
# Build dan sync dengan Capacitor
npm run cap:build

# Buka Android Studio
npm run cap:android
```

## 📋 Script NPM

- `npm run dev` - Menjalankan development server
- `npm run build` - Build aplikasi untuk production
- `npm run preview` - Preview build production
- `npm run cap:build` - Build dan sync dengan Capacitor
- `npm run cap:android` - Buka project Android di Android Studio
- `npm run cap:sync` - Sync perubahan dengan Capacitor

## 🎨 Komponen Utama

### Homepage Component

Komponen homepage menampilkan carousel produk dengan fitur:
- Slider interaktif dengan 4 produk
- Animasi smooth transition
- Detail produk dengan spesifikasi
- Tombol navigasi (previous/next)
- Mode detail view

### Navigation Component

Komponen navigasi horizontal dengan:
- 6 tab navigasi dengan ikon
- Active state indication
- Responsive design
- DaisyUI styling

## 🎯 Kustomisasi

### Mengganti Produk

Edit array `products` di `src/components/Homepage.tsx`:

```typescript
const products: ProductItem[] = [
  {
    id: 1,
    image: 'path/to/image.jpg',
    title: 'Judul Produk',
    topic: 'Nama Produk',
    description: 'Deskripsi produk...',
    // ... spesifikasi lainnya
  }
];
```

### Mengganti Tema

Edit konfigurasi DaisyUI di `tailwind.config.js`:

```javascript
daisyui: {
  themes: [
    {
      ppwa: {
        "primary": "#693EFF",
        "secondary": "#DC422A",
        // ... warna lainnya
      },
    },
  ],
}
```

## 📱 Android Deployment

1. Pastikan Android Studio terinstall
2. Jalankan `npm run cap:build`
3. Buka Android Studio dengan `npm run cap:android`
4. Build APK atau AAB dari Android Studio

## 🔧 Konfigurasi Capacitor

File `capacitor.config.ts` sudah dikonfigurasi untuk:
- HTTPS scheme untuk Android
- Mixed content support
- Optimasi untuk PWA

## 📝 Dokumentasi Kode

Semua komponen telah didokumentasikan dalam bahasa Indonesia dengan:
- JSDoc comments untuk fungsi dan interface
- Komentar inline untuk logika kompleks
- Type definitions yang jelas
- Penjelasan responsive breakpoints

## 🤝 Kontribusi

1. Fork repository ini
2. Buat branch fitur baru (`git checkout -b fitur-baru`)
3. Commit perubahan (`git commit -am 'Tambah fitur baru'`)
4. Push ke branch (`git push origin fitur-baru`)
5. Buat Pull Request

## 📄 Lisensi

Project ini menggunakan lisensi MIT. Lihat file `LICENSE` untuk detail lengkap.

## 🔍 Troubleshooting

### Build Error

Jika terjadi error saat build:
```bash
# Clear cache dan reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Android Sync Error

Jika Capacitor sync gagal:
```bash
# Clean dan rebuild
npm run build
npx cap clean android
npx cap add android
npx cap sync
```

### Responsive Issues

Untuk masalah responsive design:
- Periksa breakpoints di `src/components/Homepage.css`
- Sesuaikan ukuran font dan spacing
- Test di berbagai ukuran layar

## 📞 Support

Untuk bantuan dan pertanyaan:
- Buat issue di GitHub repository
- Dokumentasi lengkap tersedia di kode sumber
- Semua komponen memiliki komentar dalam bahasa Indonesia
