/**
 * Service Worker untuk PPWA - Progressive Web App
 * Implementasi offline-first architecture dengan caching strategies
 */

const CACHE_NAME = 'ppwa-cache-v1';
const STATIC_CACHE = 'ppwa-static-v1';
const DYNAMIC_CACHE = 'ppwa-dynamic-v1';
const DATA_CACHE = 'ppwa-data-v1';

// Daftar file yang akan di-cache untuk offline functionality
const STATIC_FILES = [
  '/',
  '/index.html',
  '/src/main.tsx',
  '/src/App.tsx',
  '/src/App.css',
  '/src/index.css',
  '/src/components/Homepage.tsx',
  '/src/components/Homepage.css',
  '/src/components/Navigation.tsx',
  '/vite.svg',
  '/images/icons/icon-192x192.png',
  '/images/product/img1.png',
  '/images/product/img2.png',
  '/images/product/img3.png',
  '/images/product/img4.png',
  // Tambahkan font dan assets lainnya
  'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'
];

// URL API yang akan di-cache secara dinamis
const API_URLS = [
  // Tambahkan URL API yang digunakan aplikasi
];

/**
 * Event listener untuk install service worker
 * Pre-cache static files
 */
self.addEventListener('install', (event) => {
  console.log('[SW] Installing Service Worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Pre-caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .catch((error) => {
        console.error('[SW] Error pre-caching static files:', error);
      })
  );
  
  // Force activation of new service worker
  self.skipWaiting();
});

/**
 * Event listener untuk activate service worker
 * Clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== DATA_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
  );
  
  // Take control of all pages immediately
  self.clients.claim();
});

/**
 * Event listener untuk fetch requests
 * Implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests with appropriate strategies
  if (isStaticAsset(request)) {
    // Cache First strategy untuk static assets
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else if (isAPIRequest(request)) {
    // Network First strategy untuk API calls
    event.respondWith(networkFirst(request, DATA_CACHE));
  } else {
    // Stale While Revalidate untuk halaman HTML
    event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
  }
});

/**
 * Cache First Strategy
 * Cek cache dulu, jika tidak ada baru fetch dari network
 */
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('[SW] Serving from cache:', request.url);
      return cachedResponse;
    }
    
    console.log('[SW] Fetching from network:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] Cache First error:', error);
    return new Response('Offline - Resource not available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

/**
 * Network First Strategy
 * Coba network dulu, jika gagal gunakan cache
 */
async function networkFirst(request, cacheName) {
  try {
    console.log('[SW] Trying network first:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', request.url);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({
      error: 'Offline - Data not available',
      offline: true
    }), {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Stale While Revalidate Strategy
 * Serve dari cache sambil update di background
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Fetch dari network di background
  const networkResponsePromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    })
    .catch(() => null);
  
  // Return cached response immediately jika ada
  if (cachedResponse) {
    console.log('[SW] Serving stale content:', request.url);
    return cachedResponse;
  }
  
  // Jika tidak ada cache, tunggu network response
  console.log('[SW] No cache, waiting for network:', request.url);
  return networkResponsePromise || new Response('Offline', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

/**
 * Helper function untuk mengecek apakah request adalah static asset
 */
function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

/**
 * Helper function untuk mengecek apakah request adalah API call
 */
function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/') || 
         API_URLS.some(apiUrl => url.href.includes(apiUrl));
}

/**
 * Background Sync untuk data synchronization
 */
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-data') {
    event.waitUntil(syncData());
  }
});

/**
 * Function untuk sync data ketika online
 */
async function syncData() {
  try {
    console.log('[SW] Starting background data sync...');
    
    // Ambil data yang pending sync dari IndexedDB
    const pendingData = await getPendingSyncData();
    
    if (pendingData.length > 0) {
      for (const data of pendingData) {
        try {
          await syncSingleItem(data);
          await markAsSynced(data.id);
        } catch (error) {
          console.error('[SW] Failed to sync item:', data.id, error);
        }
      }
    }
    
    // Update cached data dengan data terbaru
    await updateCachedData();
    
    console.log('[SW] Background sync completed');
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

/**
 * Placeholder functions untuk data sync
 * Akan diimplementasi dengan IndexedDB
 */
async function getPendingSyncData() {
  // TODO: Implement dengan IndexedDB
  return [];
}

async function syncSingleItem(data) {
  // TODO: Implement API call untuk sync data
  console.log('[SW] Syncing item:', data);
}

async function markAsSynced(id) {
  // TODO: Implement update status di IndexedDB
  console.log('[SW] Marked as synced:', id);
}

async function updateCachedData() {
  // TODO: Implement update cached data
  console.log('[SW] Updated cached data');
}

/**
 * Handle push notifications (untuk future enhancement)
 */
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New update available',
    icon: '/images/icon-192x192.png',
    badge: '/images/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    }
  };
  
  event.waitUntil(
    self.registration.showNotification('PPWA Update', options)
  );
});
