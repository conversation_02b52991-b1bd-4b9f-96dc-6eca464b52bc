{"logs": [{"outputFile": "com.ppwa.app-mergeDebugResources-31:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8ac3197f7b90dca028289edaed4b26a8\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "19027", "endColumns": "49", "endOffsets": "19072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bebc7a38cd4b5cef7e7bd02a35627fba\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "287", "startColumns": "4", "startOffsets": "18870", "endColumns": "42", "endOffsets": "18908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a16ab75ea44743ab14e8dbb157134b9a\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "21456", "endColumns": "82", "endOffsets": "21534"}}, {"source": "C:\\xampp\\htdocs\\ppwa\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,204,150,97", "endColumns": "41,58,53,52", "endOffsets": "92,258,199,145"}, "to": {"startLines": "327,353,355,358", "startColumns": "4,4,4,4", "startOffsets": "21539,24425,24564,24742", "endColumns": "41,58,53,52", "endOffsets": "21576,24479,24613,24790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c268ea5926dc9e34e2f22e439f8364c\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2038,2054,2060,3149,3165", "startColumns": "4,4,4,4,4", "startOffsets": "134186,134611,134789,172658,173069", "endLines": "2053,2059,2069,3164,3168", "endColumns": "24,24,24,24,24", "endOffsets": "134606,134784,135068,173064,173191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3182a7f1ff342bdd826c9dbae9e16b6b\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "7,8,10,11,12,13,207,208,209,210,211,212,213,296,632,633,634,1464,1466,1786,1795,1808", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "420,480,580,649,721,784,14274,14348,14424,14500,14577,14648,14717,19384,42575,42656,42748,95185,95294,119695,120155,120930", "endLines": "7,8,10,11,12,13,207,208,209,210,211,212,213,296,632,633,634,1465,1467,1794,1807,1811", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "475,534,644,716,779,851,14343,14419,14495,14572,14643,14712,14783,19447,42651,42743,42836,95289,95410,120150,120925,121198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd66131ee4a60141ca77bfc3629b887e\\transformed\\fragment-1.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "261,268,291,2805,2810", "startColumns": "4,4,4,4,4", "startOffsets": "17569,17868,19077,162292,162462", "endLines": "261,268,291,2809,2813", "endColumns": "56,64,63,24,24", "endOffsets": "17621,17928,19136,162457,162606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4e49ec6231d6a4050ebaaa942673be0d\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "2,1921,2641,2647", "startColumns": "4,4,4,4", "startOffsets": "150,130001,154506,154717", "endLines": "2,1923,2646,2730", "endColumns": "60,12,24,24", "endOffsets": "206,130141,154712,159228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5c24a62d82fcb7eb4c2846983c40de53\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "297,343", "startColumns": "4,4", "startOffsets": "19452,23132", "endColumns": "67,166", "endOffsets": "19515,23294"}}, {"source": "C:\\xampp\\htdocs\\ppwa\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "364,370,375", "startColumns": "4,4,4", "startOffsets": "25237,25521,25770", "endLines": "369,374,377", "endColumns": "12,12,12", "endOffsets": "25516,25765,25916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5c373eb2fbf7adbf337fcbc45aaa527\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,183,184,185,186,187,188,189,190,191,214,215,216,217,218,219,220,221,257,258,259,260,262,265,266,269,286,292,293,294,295,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,356,359,360,361,362,363,378,386,387,391,395,399,404,410,417,421,425,430,434,438,442,446,450,454,460,464,470,474,480,484,489,493,496,500,506,510,516,520,526,529,533,537,541,545,549,550,551,552,555,558,561,564,568,569,570,571,572,575,577,579,581,586,587,591,597,601,602,604,616,617,621,627,631,635,636,640,667,671,672,676,704,876,902,1073,1099,1130,1138,1144,1160,1182,1187,1192,1202,1211,1220,1224,1231,1250,1257,1258,1267,1270,1273,1277,1281,1285,1288,1289,1294,1299,1309,1314,1321,1327,1328,1331,1335,1340,1342,1344,1347,1350,1352,1356,1359,1366,1369,1372,1376,1378,1382,1384,1386,1388,1392,1400,1408,1420,1426,1435,1438,1449,1452,1453,1458,1459,1468,1537,1607,1608,1618,1627,1628,1630,1634,1637,1640,1643,1646,1649,1652,1655,1659,1662,1665,1668,1672,1675,1679,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1705,1707,1708,1709,1710,1711,1712,1713,1714,1716,1717,1719,1720,1722,1724,1725,1727,1728,1729,1730,1731,1732,1734,1735,1736,1737,1738,1750,1752,1754,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1770,1771,1772,1773,1774,1775,1776,1778,1782,1812,1813,1814,1815,1816,1817,1821,1822,1823,1824,1826,1828,1830,1832,1834,1835,1836,1837,1839,1841,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1857,1858,1859,1860,1862,1864,1865,1867,1868,1870,1872,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1887,1888,1889,1890,1892,1893,1894,1895,1896,1898,1900,1902,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1924,1999,2002,2005,2008,2022,2028,2070,2073,2102,2129,2138,2202,2565,2575,2613,2731,2853,2877,2883,2902,2923,3047,3067,3073,3077,3083,3137,3169,3235,3255,3310,3322,3348", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,266,311,539,856,911,973,1037,1107,1168,1243,1319,1396,1634,1719,1801,1877,1953,2030,2108,2214,2320,2399,2479,2536,3627,3701,3776,3841,3907,3967,4028,4100,4173,4240,4365,4424,4483,4542,4601,4660,4714,4768,4821,4875,4929,4983,5169,5243,5322,5395,5469,5540,5612,5684,5757,5814,5872,5945,6019,6093,6168,6240,6313,6383,6454,6514,6575,6644,6713,6783,6857,6933,6997,7074,7150,7227,7292,7361,7438,7513,7582,7650,7727,7793,7854,7951,8016,8085,8184,8255,8314,8372,8429,8488,8552,8623,8695,8767,8839,8911,8978,9046,9114,9173,9236,9300,9390,9481,9541,9607,9674,9740,9810,9874,9927,9994,10055,10122,10235,10293,10356,10421,10486,10561,10634,10706,10750,10797,10843,10892,10953,11014,11075,11137,11201,11265,11329,11394,11457,11517,11578,11644,11703,11763,11825,11896,11956,12512,12598,12685,12775,12862,12950,13032,13115,13205,14788,14840,14898,14943,15009,15073,15130,15187,17364,17421,17469,17518,17626,17730,17777,17933,18838,19141,19205,19267,19327,19590,19664,19734,19812,19866,19936,20021,20069,20115,20176,20239,20305,20369,20440,20503,20568,20632,20693,20754,20806,20879,20953,21022,21097,21171,21245,21386,24618,24795,24873,24963,25051,25147,25921,26503,26592,26839,27120,27372,27657,28050,28527,28749,28971,29247,29474,29704,29934,30164,30394,30621,31040,31266,31691,31921,32349,32568,32851,33059,33190,33417,33843,34068,34495,34716,35141,35261,35537,35838,36162,36453,36767,36904,37035,37140,37382,37549,37753,37961,38232,38344,38456,38561,38678,38892,39038,39178,39264,39612,39700,39946,40364,40613,40695,40793,41450,41550,41802,42226,42481,42841,42930,43167,45191,45433,45535,45788,47944,58625,60141,70836,72364,74121,74747,75167,76428,77693,77949,78185,78732,79226,79831,80029,80609,81977,82352,82470,83008,83165,83361,83634,83890,84060,84201,84265,84630,84997,85673,85937,86275,86628,86722,86908,87214,87476,87601,87728,87967,88178,88297,88490,88667,89122,89303,89425,89684,89797,89984,90086,90193,90322,90597,91105,91601,92478,92772,93342,93491,94223,94395,94479,94815,94907,95415,100646,106017,106079,106657,107241,107332,107445,107674,107834,107986,108157,108323,108492,108659,108822,109065,109235,109408,109579,109853,110052,110257,110587,110671,110767,110863,110961,111061,111163,111265,111367,111469,111571,111671,111767,111879,112008,112131,112262,112393,112491,112605,112699,112839,112973,113069,113181,113281,113397,113493,113605,113705,113845,113981,114145,114275,114433,114583,114724,114868,115003,115115,115265,115393,115521,115657,115789,115919,116049,116161,117059,117205,117349,117487,117553,117643,117719,117823,117913,118015,118123,118231,118331,118411,118503,118601,118711,118763,118841,118947,119039,119143,119253,119375,119538,121203,121283,121383,121473,121583,121673,121914,122008,122114,122206,122306,122418,122532,122648,122764,122858,122972,123084,123186,123306,123428,123510,123614,123734,123860,123958,124052,124140,124252,124368,124490,124602,124777,124893,124979,125071,125183,125307,125374,125500,125568,125696,125840,125968,126037,126132,126247,126360,126459,126568,126679,126790,126891,126996,127096,127226,127317,127440,127534,127646,127732,127836,127932,128020,128138,128242,128346,128472,128560,128668,128768,128858,128968,129052,129154,129238,129292,129356,129462,129548,129658,129742,130146,132762,132880,132995,133075,133436,133669,135073,135151,136495,137856,138244,141087,151140,151478,153149,159233,163460,164211,164473,164988,165367,169645,170251,170480,170631,170846,172346,173196,176222,176966,179097,179437,180748", "endLines": "3,4,5,9,14,15,16,17,18,19,20,21,22,25,26,27,28,29,30,31,32,33,34,35,36,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,183,184,185,186,187,188,189,190,191,214,215,216,217,218,219,220,221,257,258,259,260,262,265,266,269,286,292,293,294,295,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,356,359,360,361,362,363,385,386,390,394,398,403,409,416,420,424,429,433,437,441,445,449,453,459,463,469,473,479,483,488,492,495,499,505,509,515,519,525,528,532,536,540,544,548,549,550,551,554,557,560,563,567,568,569,570,571,574,576,578,580,585,586,590,596,600,601,603,615,616,620,626,630,631,635,639,666,670,671,675,703,875,901,1072,1098,1129,1137,1143,1159,1181,1186,1191,1201,1210,1219,1223,1230,1249,1256,1257,1266,1269,1272,1276,1280,1284,1287,1288,1293,1298,1308,1313,1320,1326,1327,1330,1334,1339,1341,1343,1346,1349,1351,1355,1358,1365,1368,1371,1375,1377,1381,1383,1385,1387,1391,1399,1407,1419,1425,1434,1437,1448,1451,1452,1457,1458,1463,1536,1606,1607,1617,1626,1627,1629,1633,1636,1639,1642,1645,1648,1651,1654,1658,1661,1664,1667,1671,1674,1678,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1704,1706,1707,1708,1709,1710,1711,1712,1713,1715,1716,1718,1719,1721,1723,1724,1726,1727,1728,1729,1730,1731,1733,1734,1735,1736,1737,1738,1751,1753,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1769,1770,1771,1772,1773,1774,1775,1777,1781,1785,1812,1813,1814,1815,1816,1820,1821,1822,1823,1825,1827,1829,1831,1833,1834,1835,1836,1838,1840,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1856,1857,1858,1859,1861,1863,1864,1866,1867,1869,1871,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1886,1887,1888,1889,1891,1892,1893,1894,1895,1897,1899,1901,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1998,2001,2004,2007,2021,2027,2037,2072,2101,2128,2137,2201,2564,2568,2602,2640,2748,2876,2882,2888,2922,3046,3066,3072,3076,3082,3117,3148,3234,3254,3309,3321,3347,3354", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "261,306,355,575,906,968,1032,1102,1163,1238,1314,1391,1469,1714,1796,1872,1948,2025,2103,2209,2315,2394,2474,2531,2589,3696,3771,3836,3902,3962,4023,4095,4168,4235,4303,4419,4478,4537,4596,4655,4709,4763,4816,4870,4924,4978,5032,5238,5317,5390,5464,5535,5607,5679,5752,5809,5867,5940,6014,6088,6163,6235,6308,6378,6449,6509,6570,6639,6708,6778,6852,6928,6992,7069,7145,7222,7287,7356,7433,7508,7577,7645,7722,7788,7849,7946,8011,8080,8179,8250,8309,8367,8424,8483,8547,8618,8690,8762,8834,8906,8973,9041,9109,9168,9231,9295,9385,9476,9536,9602,9669,9735,9805,9869,9922,9989,10050,10117,10230,10288,10351,10416,10481,10556,10629,10701,10745,10792,10838,10887,10948,11009,11070,11132,11196,11260,11324,11389,11452,11512,11573,11639,11698,11758,11820,11891,11951,12019,12593,12680,12770,12857,12945,13027,13110,13200,13291,14835,14893,14938,15004,15068,15125,15182,15236,17416,17464,17513,17564,17655,17772,17821,17974,18865,19200,19262,19322,19379,19659,19729,19807,19861,19931,20016,20064,20110,20171,20234,20300,20364,20435,20498,20563,20627,20688,20749,20801,20874,20948,21017,21092,21166,21240,21381,21451,24666,24868,24958,25046,25142,25232,26498,26587,26834,27115,27367,27652,28045,28522,28744,28966,29242,29469,29699,29929,30159,30389,30616,31035,31261,31686,31916,32344,32563,32846,33054,33185,33412,33838,34063,34490,34711,35136,35256,35532,35833,36157,36448,36762,36899,37030,37135,37377,37544,37748,37956,38227,38339,38451,38556,38673,38887,39033,39173,39259,39607,39695,39941,40359,40608,40690,40788,41445,41545,41797,42221,42476,42570,42925,43162,45186,45428,45530,45783,47939,58620,60136,70831,72359,74116,74742,75162,76423,77688,77944,78180,78727,79221,79826,80024,80604,81972,82347,82465,83003,83160,83356,83629,83885,84055,84196,84260,84625,84992,85668,85932,86270,86623,86717,86903,87209,87471,87596,87723,87962,88173,88292,88485,88662,89117,89298,89420,89679,89792,89979,90081,90188,90317,90592,91100,91596,92473,92767,93337,93486,94218,94390,94474,94810,94902,95180,100641,106012,106074,106652,107236,107327,107440,107669,107829,107981,108152,108318,108487,108654,108817,109060,109230,109403,109574,109848,110047,110252,110582,110666,110762,110858,110956,111056,111158,111260,111362,111464,111566,111666,111762,111874,112003,112126,112257,112388,112486,112600,112694,112834,112968,113064,113176,113276,113392,113488,113600,113700,113840,113976,114140,114270,114428,114578,114719,114863,114998,115110,115260,115388,115516,115652,115784,115914,116044,116156,116296,117200,117344,117482,117548,117638,117714,117818,117908,118010,118118,118226,118326,118406,118498,118596,118706,118758,118836,118942,119034,119138,119248,119370,119533,119690,121278,121378,121468,121578,121668,121909,122003,122109,122201,122301,122413,122527,122643,122759,122853,122967,123079,123181,123301,123423,123505,123609,123729,123855,123953,124047,124135,124247,124363,124485,124597,124772,124888,124974,125066,125178,125302,125369,125495,125563,125691,125835,125963,126032,126127,126242,126355,126454,126563,126674,126785,126886,126991,127091,127221,127312,127435,127529,127641,127727,127831,127927,128015,128133,128237,128341,128467,128555,128663,128763,128853,128963,129047,129149,129233,129287,129351,129457,129543,129653,129737,129857,132757,132875,132990,133070,133431,133664,134181,135146,136490,137851,138239,141082,151135,151270,152843,154501,159800,164206,164468,164668,165362,169640,170246,170475,170626,170841,171924,172653,176217,176961,179092,179432,180743,180946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e3dbe92fd4c85c5f7c1e0361b9a0dc1b\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "18973", "endColumns": "53", "endOffsets": "19022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b2f1ebf2ceab73ed863e67c4c2762e32\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "6,23,24,37,38,73,74,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,298,328,329,330,331,332,333,334,357,1739,1740,1744,1745,1749,1919,1920,2569,2603,2749,2784,2814,2847", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "360,1474,1546,2594,2659,5037,5106,12024,12094,12162,12234,12304,12365,12439,13296,13357,13418,13480,13544,13606,13667,13735,13835,13895,13961,14034,14103,14160,14212,15241,15313,15389,15454,15513,15572,15632,15692,15752,15812,15872,15932,15992,16052,16112,16172,16231,16291,16351,16411,16471,16531,16591,16651,16711,16771,16831,16890,16950,17010,17069,17128,17187,17246,17305,17660,17695,17979,18034,18097,18152,18210,18268,18329,18392,18449,18500,18550,18611,18668,18734,18768,18803,19520,21581,21648,21720,21789,21858,21932,22004,24671,116301,116418,116619,116729,116930,129862,129934,151275,152848,159805,161611,162611,163293", "endLines": "6,23,24,37,38,73,74,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,263,264,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,298,328,329,330,331,332,333,334,357,1739,1743,1744,1748,1749,1919,1920,2574,2612,2783,2804,2846,2852", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "415,1541,1629,2654,2720,5101,5164,12089,12157,12229,12299,12360,12434,12507,13352,13413,13475,13539,13601,13662,13730,13830,13890,13956,14029,14098,14155,14207,14269,15308,15384,15449,15508,15567,15627,15687,15747,15807,15867,15927,15987,16047,16107,16167,16226,16286,16346,16406,16466,16526,16586,16646,16706,16766,16826,16885,16945,17005,17064,17123,17182,17241,17300,17359,17690,17725,18029,18092,18147,18205,18263,18324,18387,18444,18495,18545,18606,18663,18729,18763,18798,18833,19585,21643,21715,21784,21853,21927,21999,22087,24737,116413,116614,116724,116925,117054,129929,129996,151473,153144,161606,162287,163288,163455"}}, {"source": "C:\\xampp\\htdocs\\ppwa\\node_modules\\@capacitor\\android\\capacitor\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "39,40,41,354", "startColumns": "4,4,4,4", "startOffsets": "2725,2800,2876,24484", "endColumns": "74,75,79,79", "endOffsets": "2795,2871,2951,24559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5ba4d810dceffb7c683e6aecdfcad03\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "42,43,44,45,46,47,48,49,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,2889,3118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2956,3046,3126,3216,3306,3386,3467,3547,22092,22197,22378,22503,22610,22790,22913,23029,23299,23487,23592,23773,23898,24073,24221,24284,24346,164673,171929", "endLines": "42,43,44,45,46,47,48,49,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,2901,3136", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3041,3121,3211,3301,3381,3462,3542,3622,22192,22373,22498,22605,22785,22908,23024,23127,23482,23587,23768,23893,24068,24216,24279,24341,24420,164983,172341"}}, {"source": "C:\\xampp\\htdocs\\ppwa\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "4308", "endColumns": "56", "endOffsets": "4360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bb6f7093e5d8dbbb694a41a1650ae38d\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "267,288", "startColumns": "4,4", "startOffsets": "17826,18913", "endColumns": "41,59", "endOffsets": "17863,18968"}}]}]}