# Calendar Component Test Guide

## Manual Testing Checklist

### 1. Basic Layout Testing
- [ ] Calendar page loads without errors
- [ ] Main calendar takes ~70% of screen width
- [ ] Secondary calendar and form take ~30% of screen width
- [ ] No horizontal or vertical scrolling on desktop
- [ ] Navigation buttons (previous/next month) work correctly
- [ ] FAB "HOME" button is visible at bottom-right

### 2. Calendar Display Testing
- [ ] Current month displays correctly with proper dates
- [ ] Next month displays correctly in secondary calendar
- [ ] Today's date is highlighted
- [ ] Day headers show correctly (<PERSON>, Sen, Sel, etc.)
- [ ] Dates from previous/next month are dimmed

### 3. Task Form Testing
- [ ] All form fields are present (Task Name, Date, Time)
- [ ] Form validation works (required fields)
- [ ] Task submission creates new task
- [ ] Form resets after successful submission
- [ ] Success message appears after task creation

### 4. Task Storage Testing
- [ ] Tasks are saved to IndexedDB
- [ ] Tasks persist after page refresh
- [ ] Tasks load correctly on component initialization
- [ ] Multiple tasks can be created and stored

### 5. Task Display Testing
- [ ] Tasks appear in the task list below main calendar
- [ ] Tasks are sorted by date and time
- [ ] Task items show name, date, and time
- [ ] Task status indicator shows correctly (⏰ for pending, ✓ for notified)
- [ ] Delete button works for removing tasks

### 6. Calendar Integration Testing
- [ ] Dates with tasks show task indicator dot
- [ ] Task indicator appears on correct dates
- [ ] Calendar updates when tasks are added/removed

### 7. Notification Testing
- [ ] Notification permission is requested on first load
- [ ] Notifications are scheduled when tasks are created
- [ ] Immediate notifications work for testing
- [ ] Tasks are marked as notified after notification

### 8. Responsive Design Testing

#### Desktop (1024px+)
- [ ] Layout is side-by-side (70%/30%)
- [ ] All elements are clearly visible
- [ ] No scrolling required
- [ ] Touch targets are adequate

#### Tablet (768px - 1023px)
- [ ] Layout switches to vertical stacking
- [ ] Secondary section becomes horizontal (calendar + form side-by-side)
- [ ] All content fits without scrolling
- [ ] Touch targets are 44px minimum

#### Mobile (< 768px)
- [ ] Layout is fully vertical
- [ ] Calendar grid is readable
- [ ] Form inputs are properly sized (16px font to prevent zoom)
- [ ] FAB is accessible and properly sized
- [ ] Task list is scrollable if needed

### 9. Navigation Testing
- [ ] FAB "HOME" button navigates back to home page
- [ ] Bottom navigation is hidden on calendar page
- [ ] Navigation from other pages to calendar works
- [ ] Custom navigation event handling works

### 10. Error Handling Testing
- [ ] Invalid date/time inputs are handled gracefully
- [ ] Storage errors don't crash the app
- [ ] Notification errors don't crash the app
- [ ] Network offline scenarios work correctly

## Test Data Suggestions

### Sample Tasks to Create:
1. **Meeting** - Tomorrow at 10:00 AM
2. **Lunch** - Today at 12:30 PM (for immediate notification testing)
3. **Workout** - Next week at 6:00 PM
4. **Doctor Appointment** - Next month at 2:00 PM

### Browser Testing:
- [ ] Chrome (desktop & mobile)
- [ ] Firefox (desktop & mobile)
- [ ] Safari (desktop & mobile)
- [ ] Edge (desktop)

### Device Testing:
- [ ] Desktop (1920x1080)
- [ ] Tablet (1024x768)
- [ ] Mobile (375x667)
- [ ] Large mobile (414x896)

## Known Limitations

1. **Web Notifications**: Cannot cancel scheduled notifications in web browsers
2. **Time Zone**: Uses local device time zone
3. **Recurring Tasks**: Not implemented (single occurrence only)
4. **Task Editing**: Not implemented (delete and recreate required)
5. **Bulk Operations**: Not implemented (one task at a time)

## Performance Considerations

- IndexedDB operations are asynchronous and non-blocking
- Calendar re-renders are optimized with proper React keys
- Notification checking runs every minute (configurable)
- Storage operations include error handling and logging

## Accessibility Features

- Proper ARIA labels for navigation buttons
- Keyboard navigation support
- High contrast mode support
- Touch target minimum 44px
- Screen reader friendly structure
- Focus management for form inputs

## Future Enhancements

1. Task editing functionality
2. Recurring task support
3. Task categories/colors
4. Calendar view options (week, day)
5. Task search and filtering
6. Export/import functionality
7. Sync with external calendars
8. Task priority levels
9. Reminder customization
10. Bulk task operations
