# 🔧 Troubleshooting Guide - PPWA

Panduan mengatasi masalah umum yang mungkin terjadi pada aplikasi PPWA.

## 🚨 Common Issues

### 1. Build & Development Issues

#### ❌ npm install gagal
**Symptoms:**
- Error saat `npm install`
- Dependencies tidak terinstall

**Solutions:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules dan package-lock.json
rm -rf node_modules package-lock.json

# Install ulang
npm install

# Atau gunakan yarn
yarn install
```

#### ❌ Capacitor sync gagal
**Symptoms:**
- Error saat `npx cap sync`
- Plugin tidak terdaftar

**Solutions:**
```bash
# Remove dan add ulang platform
npx cap remove android
npx cap add android

# Sync dengan force
npx cap sync --force

# Check capacitor config
npx cap doctor
```

#### ❌ TypeScript errors
**Symptoms:**
- Type errors di IDE
- Build gagal karena type issues

**Solutions:**
```bash
# Update TypeScript
npm update typescript

# Restart TypeScript server (VS Code)
Ctrl+Shift+P > "TypeScript: Restart TS Server"

# Check tsconfig.json
npx tsc --noEmit
```

### 2. Android Build Issues

#### ❌ Android SDK not found
**Symptoms:**
- `ANDROID_HOME not set`
- SDK path errors

**Solutions:**
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools

# macOS/Linux
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

#### ❌ Gradle build failed
**Symptoms:**
- Build errors di Android Studio
- Gradle sync issues

**Solutions:**
```bash
# Clean project
cd android
./gradlew clean

# Rebuild
./gradlew build

# Update Gradle wrapper
./gradlew wrapper --gradle-version=8.7
```

#### ❌ Permission denied (gradlew)
**Symptoms:**
- `Permission denied: ./gradlew`

**Solutions:**
```bash
# Fix permissions (Linux/macOS)
chmod +x android/gradlew

# Windows - run as administrator
```

#### ❌ Out of memory error
**Symptoms:**
- `OutOfMemoryError` during build

**Solutions:**
Edit `android/gradle.properties`:
```properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
```

### 3. Service Worker Issues

#### ❌ Service Worker tidak terdaftar
**Symptoms:**
- Offline functionality tidak bekerja
- Cache tidak tersimpan

**Solutions:**
```javascript
// Check registration
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
    .then(reg => console.log('SW registered:', reg))
    .catch(err => console.log('SW registration failed:', err));
}

// Debug service worker
chrome://inspect/#service-workers
```

#### ❌ Cache tidak update
**Symptoms:**
- Old content masih muncul
- Changes tidak terlihat

**Solutions:**
```javascript
// Force update service worker
self.skipWaiting();
self.clients.claim();

// Clear cache
caches.keys().then(names => {
  names.forEach(name => caches.delete(name));
});

// Hard refresh browser
Ctrl+Shift+R (Chrome)
```

### 4. Location & Device Issues

#### ❌ Geolocation tidak bekerja
**Symptoms:**
- Location permission denied
- Coordinates tidak muncul

**Solutions:**
```typescript
// Check permission status
const permission = await Geolocation.checkPermissions();
console.log('Location permission:', permission.location);

// Request permission
if (permission.location !== 'granted') {
  await Geolocation.requestPermissions();
}

// Fallback untuk web
if (!('geolocation' in navigator)) {
  console.log('Geolocation not supported');
}
```

#### ❌ Device info tidak muncul
**Symptoms:**
- Device UUID kosong
- Platform info tidak akurat

**Solutions:**
```typescript
// Check Capacitor platform
import { Capacitor } from '@capacitor/core';

if (Capacitor.isNativePlatform()) {
  // Native platform
  const info = await Device.getInfo();
} else {
  // Web platform - use fallback
  const info = {
    platform: 'web',
    model: navigator.userAgent,
    // ... fallback data
  };
}
```

### 5. Storage & Sync Issues

#### ❌ IndexedDB tidak bekerja
**Symptoms:**
- Data tidak tersimpan
- Storage errors

**Solutions:**
```typescript
// Check IndexedDB support
if (!('indexedDB' in window)) {
  console.log('IndexedDB not supported');
  // Fallback to localStorage
}

// Check storage quota
navigator.storage.estimate().then(estimate => {
  console.log('Storage:', estimate.usage, '/', estimate.quota);
});

// Clear IndexedDB
await storage.clearAll();
```

#### ❌ Background sync tidak jalan
**Symptoms:**
- Data tidak sync otomatis
- Sync timer tidak bekerja

**Solutions:**
```typescript
// Check connectivity manager
const status = connectivityManager.getStatus();
console.log('Connectivity status:', status);

// Force sync
const result = await connectivityManager.forcSync();
console.log('Sync result:', result);

// Check sync interval
connectivityManager.setSyncInterval(5); // 5 minutes
```

### 6. UI & Styling Issues

#### ❌ Tailwind CSS tidak load
**Symptoms:**
- Styling tidak muncul
- Classes tidak bekerja

**Solutions:**
```bash
# Check Tailwind config
npx tailwindcss -i ./src/index.css -o ./dist/output.css --watch

# Rebuild CSS
npm run build

# Check import di main.tsx
import './index.css'
```

#### ❌ DaisyUI components tidak bekerja
**Symptoms:**
- Components tidak styled
- Theme tidak apply

**Solutions:**
```javascript
// Check tailwind.config.js
module.exports = {
  plugins: [require("daisyui")],
  daisyui: {
    themes: ["light", "dark", "ppwa"],
  },
}

// Check HTML data-theme
<html data-theme="ppwa">
```

### 7. Performance Issues

#### ❌ App loading lambat
**Symptoms:**
- Startup time lama
- Bundle size besar

**Solutions:**
```typescript
// Code splitting
const Profile = lazy(() => import('./components/Profile'));

// Preload critical resources
<link rel="preload" href="/src/main.tsx" as="script" />

// Optimize images
// Use WebP format, compress images
```

#### ❌ Memory leaks
**Symptoms:**
- App crash setelah lama digunakan
- Performance menurun

**Solutions:**
```typescript
// Cleanup event listeners
useEffect(() => {
  const handler = () => {};
  window.addEventListener('resize', handler);
  
  return () => {
    window.removeEventListener('resize', handler);
  };
}, []);

// Cleanup connectivity manager
connectivityManager.destroy();
```

## 🛠️ Debug Tools

### Browser DevTools
```javascript
// Console debugging
console.log('[PPWA] Debug info:', data);

// Network tab
// - Check failed requests
// - Monitor API calls
// - Test offline mode

// Application tab
// - Check Service Workers
// - View IndexedDB data
// - Check Local Storage
```

### Capacitor Debugging
```bash
# iOS Safari (for iOS debugging)
# Enable Web Inspector in iOS Settings

# Chrome DevTools (for Android)
chrome://inspect/#devices

# Capacitor logs
npx cap run android --livereload --external
```

### Storage Debugging
```typescript
// View IndexedDB data
const products = await storage.getProducts();
const preferences = await storage.getPreferences();
const syncQueue = await storage.getPendingSyncItems();

console.log('Products:', products);
console.log('Preferences:', preferences);
console.log('Sync queue:', syncQueue);
```

## 📊 Monitoring & Logging

### Enable Debug Mode
```typescript
// Set debug flag
localStorage.setItem('ppwa-debug', 'true');

// Check debug mode
const isDebug = localStorage.getItem('ppwa-debug') === 'true';
if (isDebug) {
  console.log('[DEBUG] Detailed logging enabled');
}
```

### Performance Monitoring
```typescript
// Measure performance
const start = performance.now();
await someAsyncOperation();
const end = performance.now();
console.log(`Operation took ${end - start} milliseconds`);

// Monitor memory usage
console.log('Memory usage:', performance.memory);
```

## 🆘 Getting Help

### Before Asking for Help
1. **Check Console Errors**
   - Open browser DevTools (F12)
   - Look for red errors in Console tab

2. **Verify Environment**
   - Node.js version: `node --version`
   - npm version: `npm --version`
   - Android SDK: `adb --version`

3. **Test in Different Environments**
   - Different browsers
   - Different devices
   - Different network conditions

### Information to Include
When reporting issues, include:
- **Error messages** (full stack trace)
- **Steps to reproduce**
- **Environment details** (OS, browser, versions)
- **Expected vs actual behavior**
- **Screenshots/videos** if applicable

### Useful Commands
```bash
# System info
node --version
npm --version
npx cap doctor

# Project info
npm list --depth=0
npx cap info

# Logs
adb logcat | grep "com.ppwa.app"
```

## 🔄 Reset & Clean Install

### Complete Reset
```bash
# 1. Clean npm
npm cache clean --force
rm -rf node_modules package-lock.json

# 2. Clean Capacitor
npx cap remove android
rm -rf android

# 3. Fresh install
npm install
npx cap add android
npx cap sync

# 4. Clean browser data
# Clear cache, cookies, local storage
```

### Selective Reset
```bash
# Reset only Android
npx cap remove android
npx cap add android
npx cap sync android

# Reset only dependencies
rm -rf node_modules
npm install

# Reset only storage
// In browser console:
localStorage.clear();
// Clear IndexedDB manually
```

---

**📞 Need More Help?**
- Check [GitHub Issues](https://github.com/your-repo/ppwa/issues)
- Read [Capacitor Documentation](https://capacitorjs.com/docs)
- Join [Discord Community](https://discord.gg/capacitor)
